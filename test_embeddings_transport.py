#!/usr/bin/env python3
"""
测试embeddings的transport参数
"""

import sys
import os
import time

def test_embeddings_rest_transport():
    """测试embeddings使用REST传输"""
    print("=== 测试Embeddings REST传输 ===")
    
    try:
        from langchain_google_genai import GoogleGenerativeAIEmbeddings
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        print("创建使用REST传输的Embeddings实例...")
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=api_key,
            request_timeout=30.0,
            max_retries=2,
            transport="rest"
        )
        
        print("Embeddings实例创建成功")
        print(f"Embeddings配置: {embeddings}")
        
        print("测试文本嵌入...")
        start_time = time.time()
        
        # 测试简单的文本嵌入
        test_texts = ["Hello world", "This is a test"]
        embeddings_result = embeddings.embed_documents(test_texts)
        
        end_time = time.time()
        print(f"✅ 嵌入成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"嵌入维度: {len(embeddings_result[0])}")
        print(f"嵌入数量: {len(embeddings_result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_embeddings_default_transport():
    """测试embeddings使用默认传输（gRPC）"""
    print("\n=== 测试Embeddings默认传输（gRPC） ===")
    
    try:
        from langchain_google_genai import GoogleGenerativeAIEmbeddings
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        print("创建使用默认传输的Embeddings实例...")
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=api_key,
            request_timeout=10.0,  # 较短超时
            max_retries=1
        )
        
        print("Embeddings实例创建成功")
        
        print("测试文本嵌入（应该会超时）...")
        start_time = time.time()
        
        test_texts = ["Hello world"]
        embeddings_result = embeddings.embed_documents(test_texts)
        
        end_time = time.time()
        print(f"✅ 意外成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"嵌入维度: {len(embeddings_result[0])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预期的失败: {str(e)}")
        return False

def test_vectorstore_creation():
    """测试向量存储创建"""
    print("\n=== 测试向量存储创建 ===")
    
    try:
        from langchain_google_genai import GoogleGenerativeAIEmbeddings
        from langchain_community.vectorstores import FAISS
        from langchain.schema import Document
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        print("创建使用REST传输的Embeddings...")
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=api_key,
            request_timeout=30.0,
            max_retries=2,
            transport="rest"
        )
        
        print("创建测试文档...")
        documents = [
            Document(page_content="This is a test document about software engineering."),
            Document(page_content="MathWorks is a company that develops mathematical software."),
            Document(page_content="The job requires experience in Python and machine learning.")
        ]
        
        print("创建FAISS向量存储...")
        start_time = time.time()
        
        vectorstore = FAISS.from_documents(documents=documents, embedding=embeddings)
        
        end_time = time.time()
        print(f"✅ 向量存储创建成功! 耗时: {end_time - start_time:.2f}秒")
        
        # 测试相似性搜索
        print("测试相似性搜索...")
        results = vectorstore.similarity_search("software engineering", k=2)
        print(f"搜索结果数量: {len(results)}")
        for i, doc in enumerate(results):
            print(f"结果 {i+1}: {doc.page_content[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量存储创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Embeddings Transport参数测试")
    print("=" * 50)
    
    # 测试REST传输
    rest_success = test_embeddings_rest_transport()
    
    # 测试默认传输
    grpc_success = test_embeddings_default_transport()
    
    # 测试向量存储创建
    vectorstore_success = test_vectorstore_creation()
    
    print("\n=== 总结 ===")
    print(f"Embeddings REST传输: {'✅ 成功' if rest_success else '❌ 失败'}")
    print(f"Embeddings 默认传输: {'✅ 成功' if grpc_success else '❌ 失败（预期）'}")
    print(f"向量存储创建: {'✅ 成功' if vectorstore_success else '❌ 失败'}")
    
    if rest_success and vectorstore_success and not grpc_success:
        print("\n🎉 transport='rest'参数对embeddings生效！")
    elif rest_success and vectorstore_success and grpc_success:
        print("\n⚠️ 所有传输都成功，可能网络环境已改善")
    else:
        print("\n❌ 仍有问题需要解决")

if __name__ == "__main__":
    main()
