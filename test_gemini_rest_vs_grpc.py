#!/usr/bin/env python3
"""
测试Gemini REST API vs gRPC
"""

import requests
import json
import sys
import os

def test_gemini_rest_api():
    """测试Gemini REST API"""
    print("=== 测试Gemini REST API ===")
    
    api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
    
    # REST API端点
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    payload = {
        "contents": [{
            "parts": [{"text": "Hello, please respond with 'REST API working'"}]
        }]
    }
    
    try:
        response = requests.post(
            url, 
            json=payload,
            timeout=30,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            text = result['candidates'][0]['content']['parts'][0]['text']
            print(f"✅ REST API成功: {text}")
            return True
        else:
            print(f"❌ REST API失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ REST API异常: {e}")
        return False

def test_langchain_with_rest_transport():
    """测试LangChain是否可以使用REST传输"""
    print("\n=== 测试LangChain REST传输 ===")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        import google.ai.generativelanguage as glm
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        # 尝试不同的传输方式
        transport_options = [
            {"transport": "rest"},
            {"transport": "grpc"},
            {}  # 默认
        ]
        
        for i, options in enumerate(transport_options):
            print(f"\n--- 传输选项 {i+1}: {options or '默认'} ---")
            
            try:
                # 尝试创建客户端
                if options:
                    # 这可能不是正确的参数，但让我们试试
                    llm = ChatGoogleGenerativeAI(
                        model="gemini-1.5-flash",
                        google_api_key=api_key,
                        temperature=0.4,
                        **options
                    )
                else:
                    llm = ChatGoogleGenerativeAI(
                        model="gemini-1.5-flash",
                        google_api_key=api_key,
                        temperature=0.4
                    )
                
                print("✅ LLM实例创建成功")
                
                # 测试调用
                response = llm.invoke("Hello")
                print(f"✅ 调用成功: {response.content}")
                return True
                
            except Exception as e:
                print(f"❌ 传输选项失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ LangChain测试失败: {e}")
        return False

def test_google_genai_direct():
    """测试直接使用google-generativeai库"""
    print("\n=== 测试google-generativeai库 ===")
    
    try:
        import google.generativeai as genai
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        # 配置API密钥
        genai.configure(api_key=api_key)
        
        # 创建模型
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # 测试生成
        response = model.generate_content("Hello, please respond with 'Direct API working'")
        print(f"✅ 直接API成功: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ 直接API失败: {e}")
        return False

def check_grpc_connectivity():
    """检查gRPC连接性"""
    print("\n=== 检查gRPC连接性 ===")
    
    try:
        import grpc
        import socket
        
        # 测试gRPC端口连接
        grpc_hosts = [
            ("generativelanguage.googleapis.com", 443),
            ("googleapis.com", 443)
        ]
        
        for host, port in grpc_hosts:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    print(f"✅ {host}:{port} - TCP连接成功")
                    
                    # 尝试gRPC连接
                    try:
                        channel = grpc.secure_channel(f"{host}:{port}", grpc.ssl_channel_credentials())
                        grpc.channel_ready_future(channel).result(timeout=10)
                        print(f"✅ {host}:{port} - gRPC连接成功")
                        channel.close()
                    except Exception as e:
                        print(f"❌ {host}:{port} - gRPC连接失败: {e}")
                else:
                    print(f"❌ {host}:{port} - TCP连接失败")
                    
            except Exception as e:
                print(f"❌ {host}:{port} - 连接异常: {e}")
        
    except ImportError:
        print("❌ grpc库未安装")
    except Exception as e:
        print(f"❌ gRPC测试失败: {e}")

def main():
    print("Gemini API传输方式测试")
    print("=" * 50)
    
    # 测试REST API
    rest_success = test_gemini_rest_api()
    
    # 检查gRPC连接性
    check_grpc_connectivity()
    
    # 测试直接API
    direct_success = test_google_genai_direct()
    
    # 测试LangChain传输选项
    langchain_success = test_langchain_with_rest_transport()
    
    print("\n=== 总结 ===")
    print(f"REST API: {'✅ 成功' if rest_success else '❌ 失败'}")
    print(f"直接API: {'✅ 成功' if direct_success else '❌ 失败'}")
    print(f"LangChain: {'✅ 成功' if langchain_success else '❌ 失败'}")
    
    if rest_success and not langchain_success:
        print("\n💡 建议: REST API可用但LangChain失败，可能需要配置LangChain使用REST传输")

if __name__ == "__main__":
    main()
