#!/usr/bin/env python3
"""
Playwright网络连接问题修复测试
专门解决ERR_SOCKET_NOT_CONNECTED问题
"""

import logging
import time
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_playwright_network_simple():
    """测试Playwright网络连接（简化版）"""
    logger.info("=== 测试Playwright网络连接（简化版） ===")
    try:
        from playwright.sync_api import sync_playwright
        
        logger.info("1. 启动Playwright...")
        with sync_playwright() as p:
            logger.info("2. 启动浏览器（最简配置）...")
            # 使用最简单的配置，不设置任何代理或特殊选项
            browser = p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            logger.info("3. 创建上下文...")
            context = browser.new_context(
                ignore_https_errors=True,
                bypass_csp=True
            )
            
            logger.info("4. 创建页面...")
            page = context.new_page()
            
            # 测试网络连接
            logger.info("5. 测试网络连接...")
            
            # 先测试简单的网站
            test_sites = [
                "https://www.google.com",
                "https://httpbin.org/get",
                "https://www.linkedin.com"
            ]
            
            for site in test_sites:
                try:
                    logger.info(f"访问: {site}")
                    page.goto(site, timeout=30000, wait_until="domcontentloaded")
                    title = page.title()
                    logger.info(f"✅ 成功访问 {site}, 标题: {title}")
                    time.sleep(2)
                except Exception as e:
                    logger.error(f"❌ 访问 {site} 失败: {str(e)}")
            
            logger.info("6. 清理资源...")
            browser.close()
            
        logger.info("✅ Playwright网络测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ Playwright网络测试失败: {str(e)}")
        return False

def test_playwright_with_linkedin_automation():
    """测试Playwright与LinkedIn自动化的集成"""
    logger.info("=== 测试Playwright与LinkedIn自动化集成 ===")
    try:
        from src.linkedin_automation_playwright import LinkedInAutomationPlaywright
        
        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        logger.info("1. 创建LinkedIn自动化实例...")
        linkedin = LinkedInAutomationPlaywright(config)
        
        logger.info("2. 设置浏览器（不使用代理）...")
        # 强制不使用代理
        page = linkedin.setup_driver(headless=False, proxy=None)
        
        logger.info("3. 测试基本网络连接...")
        try:
            # 先访问Google测试网络
            page.goto("https://www.google.com", timeout=30000)
            logger.info("✅ Google访问成功")
            
            # 再访问LinkedIn
            page.goto("https://www.linkedin.com", timeout=30000)
            title = page.title()
            logger.info(f"✅ LinkedIn访问成功, 标题: {title}")
            
        except Exception as e:
            logger.error(f"❌ 网络访问失败: {str(e)}")
            # 尝试修复：重新创建浏览器实例
            logger.info("尝试重新创建浏览器实例...")
            linkedin.close()
            
            # 修改配置，禁用所有可能导致网络问题的选项
            linkedin._PROXY_POOL = []  # 清空代理池
            page = linkedin.setup_driver(headless=False, proxy=None, force_restart=True)
            
            # 再次尝试
            page.goto("https://www.linkedin.com", timeout=30000)
            title = page.title()
            logger.info(f"✅ LinkedIn访问成功（重试后）, 标题: {title}")
        
        logger.info("4. 清理资源...")
        linkedin.close()
        
        logger.info("✅ LinkedIn自动化集成测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ LinkedIn自动化集成测试失败: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_playwright_browser_options():
    """测试不同的Playwright浏览器选项"""
    logger.info("=== 测试不同的Playwright浏览器选项 ===")
    
    test_configs = [
        {
            "name": "默认配置",
            "args": {}
        },
        {
            "name": "无沙盒模式",
            "args": {
                "args": [
                    '--no-sandbox',
                    '--disable-setuid-sandbox'
                ]
            }
        },
        {
            "name": "禁用安全功能",
            "args": {
                "args": [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage'
                ]
            }
        }
    ]
    
    from playwright.sync_api import sync_playwright
    
    for config in test_configs:
        logger.info(f"测试配置: {config['name']}")
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=False, **config['args'])
                context = browser.new_context(ignore_https_errors=True)
                page = context.new_page()
                
                # 测试访问LinkedIn
                page.goto("https://www.linkedin.com", timeout=30000)
                title = page.title()
                logger.info(f"✅ {config['name']} 成功, 标题: {title}")
                
                browser.close()
                return True  # 找到可用配置就返回
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 失败: {str(e)}")
            continue
    
    logger.error("所有配置都失败了")
    return False

def main():
    """主函数"""
    logger.info("开始Playwright网络连接问题修复测试...")
    
    # 测试1: 简单网络连接
    logger.info("\n" + "="*50)
    simple_success = test_playwright_network_simple()
    
    # 测试2: 不同浏览器选项
    if not simple_success:
        logger.info("\n" + "="*50)
        options_success = test_playwright_browser_options()
    else:
        options_success = True
    
    # 测试3: LinkedIn自动化集成
    if simple_success or options_success:
        logger.info("\n" + "="*50)
        integration_success = test_playwright_with_linkedin_automation()
    else:
        integration_success = False
    
    # 总结
    logger.info("\n" + "="*50)
    logger.info("=== 测试总结 ===")
    logger.info(f"简单网络测试: {'✅ 成功' if simple_success else '❌ 失败'}")
    logger.info(f"浏览器选项测试: {'✅ 成功' if options_success else '❌ 失败'}")
    logger.info(f"LinkedIn集成测试: {'✅ 成功' if integration_success else '❌ 失败'}")
    
    if integration_success:
        logger.info("🎉 所有测试通过！Playwright网络问题已解决")
    else:
        logger.error("❌ 仍有问题需要解决")
        logger.info("建议检查:")
        logger.info("1. 网络连接是否正常")
        logger.info("2. 防火墙设置")
        logger.info("3. 代理配置")
        logger.info("4. DNS设置")

if __name__ == "__main__":
    main()
