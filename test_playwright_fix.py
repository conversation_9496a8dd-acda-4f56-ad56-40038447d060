#!/usr/bin/env python3
"""
测试修复后的Playwright LinkedIn自动化功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.linkedin_automation_playwright import LinkedInAutomationPlaywright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_playwright_browser_management():
    """测试Playwright浏览器管理"""
    logger.info("测试Playwright浏览器管理...")
    
    try:
        # 创建LinkedIn自动化实例
        test_config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        linkedin = LinkedInAutomationPlaywright(test_config)
        
        # 测试1: 初始化浏览器
        logger.info("测试1: 初始化浏览器")
        page = linkedin.setup_driver()
        assert page is not None, "浏览器初始化失败"
        logger.info("✅ 浏览器初始化成功")
        
        # 测试2: 访问LinkedIn主页
        logger.info("测试2: 访问LinkedIn主页")
        page.goto("https://www.linkedin.com")
        time.sleep(2)
        title = page.title()
        logger.info(f"页面标题: {title}")
        # 检查是否是LinkedIn相关页面（包括中文版）
        linkedin_indicators = ["LinkedIn", "领英", "linkedin"]
        is_linkedin_page = any(indicator in title for indicator in linkedin_indicators)
        assert is_linkedin_page, f"LinkedIn页面加载失败，页面标题: {title}"
        logger.info("✅ LinkedIn主页访问成功")
        
        # 测试3: 重复调用setup_driver（不应该重启浏览器）
        logger.info("测试3: 重复调用setup_driver")
        old_page_url = page.url
        page2 = linkedin.setup_driver()  # 应该返回同一个页面实例
        new_page_url = page2.url
        assert old_page_url == new_page_url, "浏览器被意外重启"
        logger.info("✅ 浏览器实例复用成功")
        
        # 测试4: 强制重启浏览器
        logger.info("测试4: 强制重启浏览器")
        page3 = linkedin.setup_driver(force_restart=True)
        page3.goto("https://www.linkedin.com")
        time.sleep(2)
        title3 = page3.title()
        # 检查是否是LinkedIn相关页面
        linkedin_indicators = ["LinkedIn", "领英", "linkedin"]
        is_linkedin_page = any(indicator in title3 for indicator in linkedin_indicators)
        assert is_linkedin_page, f"强制重启后页面加载失败，页面标题: {title3}"
        logger.info("✅ 强制重启浏览器成功")
        
        # 测试5: 登录功能
        logger.info("测试5: 登录功能")
        login_result = linkedin.login()
        logger.info(f"登录结果: {login_result}")
        
        if login_result.get('success'):
            logger.info("✅ 登录成功")
        elif login_result.get('requires_action'):
            logger.info("⚠️ 需要用户操作（如2FA验证）")
        else:
            logger.warning(f"❌ 登录失败: {login_result.get('status')}")
        
        # 测试6: 简单的职位搜索
        logger.info("测试6: 简单的职位搜索")
        try:
            jobs = linkedin.search_jobs(
                keywords='python developer',
                location='United States',
                easy_apply_only=False
            )
            logger.info(f"找到 {len(jobs)} 个职位")
            
            if len(jobs) > 0:
                logger.info("✅ 职位搜索成功")
                # 显示前3个职位
                for i, job in enumerate(jobs[:3]):
                    logger.info(f"职位 {i+1}: {job.get('title')} - {job.get('company')}")
            else:
                logger.warning("⚠️ 未找到职位，可能需要登录或页面结构变化")
        except Exception as e:
            logger.error(f"职位搜索失败: {str(e)}")
        
        # 关闭浏览器
        linkedin.close()
        logger.info("✅ 浏览器关闭成功")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return False

def test_context_manager():
    """测试上下文管理器"""
    logger.info("测试上下文管理器...")
    
    try:
        test_config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        # 使用with语句
        with LinkedInAutomationPlaywright(test_config) as linkedin:
            page = linkedin.setup_driver()
            page.goto("https://www.linkedin.com")
            time.sleep(2)
            title = page.title()
            logger.info(f"页面标题: {title}")
            # 检查是否是LinkedIn相关页面
            linkedin_indicators = ["LinkedIn", "领英", "linkedin"]
            is_linkedin_page = any(indicator in title for indicator in linkedin_indicators)
            assert is_linkedin_page, f"LinkedIn页面加载失败，页面标题: {title}"
        
        # 退出with语句后，浏览器应该自动关闭
        logger.info("✅ 上下文管理器测试成功")
        return True
        
    except Exception as e:
        logger.error(f"上下文管理器测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 Playwright LinkedIn自动化修复验证")
    print("=" * 50)
    
    # 创建log目录
    os.makedirs('log', exist_ok=True)
    
    # 测试1: 浏览器管理
    print("\n🌐 测试1: Playwright浏览器管理")
    browser_success = test_playwright_browser_management()
    
    # 测试2: 上下文管理器
    print("\n📦 测试2: 上下文管理器")
    context_success = test_context_manager()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 修复验证结果:")
    print(f"浏览器管理: {'✅ 成功' if browser_success else '❌ 失败'}")
    print(f"上下文管理器: {'✅ 成功' if context_success else '❌ 失败'}")
    
    if browser_success and context_success:
        print("\n🎉 Playwright LinkedIn自动化修复成功！")
        print("💡 修复内容：")
        print("  - 修复了浏览器意外关闭的问题")
        print("  - 优化了浏览器实例复用机制")
        print("  - 改进了风控检测和重试逻辑")
        print("  - 增强了反检测能力")
        print("\n🚀 现在可以稳定使用Playwright进行LinkedIn自动化了！")
    else:
        print("\n⚠️ 还有一些问题需要解决")
        print("💡 建议：")
        print("  - 检查Playwright安装是否正确")
        print("  - 确认网络连接正常")
        print("  - 查看详细错误日志")

if __name__ == "__main__":
    main()
