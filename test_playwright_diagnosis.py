#!/usr/bin/env python3
"""
Playwright问题诊断测试
用于分析和解决Playwright异步/同步问题
"""

import asyncio
import sys
import traceback
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_playwright_sync():
    """测试Playwright同步版本"""
    logger.info("=== 测试Playwright同步版本 ===")
    try:
        from playwright.sync_api import sync_playwright
        
        logger.info("1. 导入sync_playwright成功")
        
        # 检查是否在事件循环中
        try:
            loop = asyncio.get_running_loop()
            logger.warning(f"检测到运行中的事件循环: {loop}")
            # 尝试使用nest_asyncio解决
            try:
                import nest_asyncio
                nest_asyncio.apply()
                logger.info("应用nest_asyncio成功")
            except ImportError:
                logger.error("需要安装nest_asyncio: pip install nest_asyncio")
                return False
        except RuntimeError:
            logger.info("没有运行中的事件循环，可以正常使用同步API")
        
        # 启动Playwright
        logger.info("2. 启动Playwright...")
        playwright = sync_playwright().start()
        logger.info("Playwright启动成功")
        
        # 启动浏览器
        logger.info("3. 启动浏览器...")
        browser = playwright.chromium.launch(headless=False)
        logger.info("浏览器启动成功")
        
        # 创建上下文
        logger.info("4. 创建上下文...")
        context = browser.new_context()
        logger.info("上下文创建成功")
        
        # 创建页面
        logger.info("5. 创建页面...")
        page = context.new_page()
        logger.info("页面创建成功")
        
        # 访问测试页面
        logger.info("6. 访问测试页面...")
        page.goto("https://www.google.com")
        title = page.title()
        logger.info(f"页面标题: {title}")
        
        # 清理
        logger.info("7. 清理资源...")
        browser.close()
        playwright.stop()
        logger.info("✅ Playwright同步版本测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Playwright同步版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

async def test_playwright_async():
    """测试Playwright异步版本"""
    logger.info("=== 测试Playwright异步版本 ===")
    try:
        from playwright.async_api import async_playwright
        
        logger.info("1. 导入async_playwright成功")
        
        # 启动Playwright
        logger.info("2. 启动异步Playwright...")
        playwright = await async_playwright().start()
        logger.info("异步Playwright启动成功")
        
        # 启动浏览器
        logger.info("3. 启动异步浏览器...")
        browser = await playwright.chromium.launch(headless=False)
        logger.info("异步浏览器启动成功")
        
        # 创建上下文
        logger.info("4. 创建异步上下文...")
        context = await browser.new_context()
        logger.info("异步上下文创建成功")
        
        # 创建页面
        logger.info("5. 创建异步页面...")
        page = await context.new_page()
        logger.info("异步页面创建成功")
        
        # 访问测试页面
        logger.info("6. 访问测试页面...")
        await page.goto("https://www.google.com")
        title = await page.title()
        logger.info(f"页面标题: {title}")
        
        # 清理
        logger.info("7. 清理异步资源...")
        await browser.close()
        await playwright.stop()
        logger.info("✅ Playwright异步版本测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Playwright异步版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_environment():
    """测试环境检查"""
    logger.info("=== 环境检查 ===")
    
    # Python版本
    logger.info(f"Python版本: {sys.version}")
    
    # 检查Playwright安装
    try:
        import playwright
        try:
            logger.info(f"Playwright版本: {playwright.__version__}")
        except AttributeError:
            logger.info("Playwright已安装（版本信息不可用）")
    except ImportError:
        logger.error("Playwright未安装")
        return False
    
    # 检查浏览器安装
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browsers = []
            if p.chromium.executable_path:
                browsers.append("Chromium")
            if p.firefox.executable_path:
                browsers.append("Firefox")
            if p.webkit.executable_path:
                browsers.append("WebKit")
            logger.info(f"已安装的浏览器: {', '.join(browsers) if browsers else '无'}")
    except Exception as e:
        logger.error(f"检查浏览器安装失败: {str(e)}")
    
    # 检查nest_asyncio
    try:
        import nest_asyncio
        logger.info("nest_asyncio已安装")
    except ImportError:
        logger.warning("nest_asyncio未安装，可能导致同步/异步冲突")
    
    return True

def run_async_test():
    """运行异步测试"""
    try:
        # 检查是否已有事件循环
        try:
            loop = asyncio.get_running_loop()
            logger.info("检测到运行中的事件循环，使用现有循环")
            # 在现有循环中创建任务
            task = asyncio.create_task(test_playwright_async())
            return asyncio.run_coroutine_threadsafe(task, loop).result()
        except RuntimeError:
            # 没有运行中的循环，创建新的
            logger.info("没有运行中的事件循环，创建新循环")
            return asyncio.run(test_playwright_async())
    except Exception as e:
        logger.error(f"运行异步测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始Playwright问题诊断...")
    
    # 环境检查
    if not test_environment():
        logger.error("环境检查失败，退出测试")
        return
    
    # 测试同步版本
    sync_success = test_playwright_sync()
    
    # 测试异步版本
    async_success = run_async_test()
    
    # 总结
    logger.info("=== 测试总结 ===")
    logger.info(f"同步版本: {'✅ 成功' if sync_success else '❌ 失败'}")
    logger.info(f"异步版本: {'✅ 成功' if async_success else '❌ 失败'}")
    
    if not sync_success and not async_success:
        logger.error("所有测试都失败了，请检查Playwright安装")
        logger.info("建议运行: python -m playwright install chromium")
    elif sync_success and not async_success:
        logger.warning("只有同步版本成功，异步版本可能有事件循环冲突")
        logger.info("建议安装: pip install nest_asyncio")
    elif not sync_success and async_success:
        logger.warning("只有异步版本成功，同步版本可能有问题")
    else:
        logger.info("所有测试都成功！")

if __name__ == "__main__":
    main()
