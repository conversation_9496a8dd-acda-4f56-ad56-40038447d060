#!/usr/bin/env python3
"""
直接测试LangChain Gemini调用
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_langchain_gemini_basic():
    """测试基本的LangChain Gemini调用"""
    print("=== 测试基本LangChain Gemini调用 ===")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.prompts import ChatPromptTemplate
        from langchain_core.output_parsers import StrOutputParser
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        # 测试不同的配置
        configs = [
            {
                "name": "默认配置",
                "model": "gemini-1.5-flash",
                "temperature": 0.4,
                "request_timeout": 30.0,
                "max_retries": 2
            },
            {
                "name": "简化配置",
                "model": "gemini-1.5-flash",
                "temperature": 0.0
            },
            {
                "name": "Pro模型",
                "model": "gemini-1.5-pro",
                "temperature": 0.4,
                "request_timeout": 30.0,
                "max_retries": 2
            }
        ]
        
        for config in configs:
            print(f"\n--- 测试配置: {config['name']} ---")
            try:
                # 创建LLM实例
                llm_params = {k: v for k, v in config.items() if k != 'name'}
                llm = ChatGoogleGenerativeAI(
                    google_api_key=api_key,
                    **llm_params
                )
                
                print(f"✅ LLM实例创建成功")
                
                # 测试简单调用
                start_time = time.time()
                response = llm.invoke("Hello, please respond with 'Test successful'")
                end_time = time.time()
                
                print(f"✅ 调用成功! 耗时: {end_time - start_time:.2f}秒")
                print(f"响应: {response.content}")
                
                # 测试链式调用
                prompt = ChatPromptTemplate.from_template("Extract the company name from: {text}")
                chain = prompt | llm | StrOutputParser()
                
                test_text = "MathWorks is hiring a Software Engineer in Boston."
                result = chain.invoke({"text": test_text})
                print(f"✅ 链式调用成功: {result}")
                
                # 如果成功，就不需要测试其他配置了
                return True
                
            except Exception as e:
                print(f"❌ 配置失败: {str(e)}")
                continue
        
        return False
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_langchain_gemini_with_proxy():
    """测试使用代理的LangChain Gemini调用"""
    print("\n=== 测试使用代理的LangChain Gemini调用 ===")
    
    # 尝试设置一些常见的代理配置
    proxy_configs = [
        None,  # 无代理
        {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"},  # 常见代理端口
        {"http": "http://127.0.0.1:1080", "https": "http://127.0.0.1:1080"},  # 另一个常见端口
    ]
    
    for i, proxy_config in enumerate(proxy_configs):
        print(f"\n--- 代理配置 {i+1}: {proxy_config or '无代理'} ---")
        
        # 临时设置环境变量
        old_env = {}
        if proxy_config:
            for key, value in proxy_config.items():
                old_env[key.upper() + '_PROXY'] = os.environ.get(key.upper() + '_PROXY')
                os.environ[key.upper() + '_PROXY'] = value
        
        try:
            success = test_langchain_gemini_basic()
            if success:
                print(f"✅ 代理配置 {i+1} 成功!")
                return True
        except Exception as e:
            print(f"❌ 代理配置 {i+1} 失败: {e}")
        finally:
            # 恢复环境变量
            if proxy_config:
                for key in proxy_config.keys():
                    env_key = key.upper() + '_PROXY'
                    if old_env[env_key] is not None:
                        os.environ[env_key] = old_env[env_key]
                    elif env_key in os.environ:
                        del os.environ[env_key]
    
    return False

def test_langchain_gemini_verbose():
    """详细测试LangChain Gemini调用过程"""
    print("\n=== 详细测试LangChain Gemini调用过程 ===")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        import logging
        
        # 启用详细日志
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger('langchain_google_genai')
        logger.setLevel(logging.DEBUG)
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        print("创建LLM实例...")
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=api_key,
            temperature=0.4,
            request_timeout=30.0,
            max_retries=2,
            verbose=True
        )
        
        print("发送测试消息...")
        response = llm.invoke("Hello")
        print(f"响应: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 详细测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("LangChain Gemini 直接调用测试")
    print("=" * 50)
    
    # 测试基本调用
    success = test_langchain_gemini_basic()
    
    if not success:
        print("\n基本调用失败，尝试代理配置...")
        success = test_langchain_gemini_with_proxy()
    
    if not success:
        print("\n所有配置都失败，进行详细测试...")
        test_langchain_gemini_verbose()
    
    if success:
        print("\n🎉 LangChain Gemini调用成功!")
    else:
        print("\n❌ LangChain Gemini调用失败")

if __name__ == "__main__":
    main()
