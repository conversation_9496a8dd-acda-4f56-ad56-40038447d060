import os
import tempfile
import textwrap
import time
import re  # For email validation
from src.libs.resume_and_cover_builder.utils import LoggerChatModel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger
from pathlib import Path
from langchain_core.prompt_values import StringPromptValue
from langchain_core.runnables import RunnablePassthrough
from langchain_text_splitters import TokenTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS
from src.libs.resume_and_cover_builder.config import global_config
from langchain_community.document_loaders import TextLoader
from requests.exceptions import HTTPError as HTTPStatusError  # HTTP error handling

# Load environment variables from the .env file
load_dotenv()

# Configure the log file
log_folder = 'log/resume/gpt_resume'
if not os.path.exists(log_folder):
    os.makedirs(log_folder)
log_path = Path(log_folder).resolve()
logger.add(log_path / "gpt_resume.log", rotation="1 day", compression="zip", retention="7 days", level="DEBUG")


class LLMParser:
    def __init__(self, openai_api_key):
        # 参数名保持为openai_api_key以保持向后兼容，但内部使用google_api_key
        google_api_key = openai_api_key

        # 网络连接配置
        import httpx

        # 创建自定义的HTTP客户端，增加重试和超时配置
        http_client = httpx.Client(
            timeout=httpx.Timeout(
                connect=30.0,  # 连接超时30秒
                read=120.0,    # 读取超时120秒
                write=30.0,    # 写入超时30秒
                pool=300.0     # 连接池超时300秒
            ),
            limits=httpx.Limits(
                max_keepalive_connections=5,
                max_connections=10,
                keepalive_expiry=30.0
            ),
            # 添加重试配置
            transport=httpx.HTTPTransport(retries=3)
        )

        try:
            # 使用Gemini模型替代OpenAI模型，增加网络配置
            self.llm = LoggerChatModel(
                ChatGoogleGenerativeAI(
                    model="gemini-2.5-flash-preview-05-20",
                    google_api_key=google_api_key,
                    temperature=0.8,
                    request_timeout=120.0,  # 请求超时120秒
                    max_retries=3,  # 最大重试3次
                    # transport="rest"  # 使用REST传输而不是gRPC
                )
            )

            # 使用Google Generative AI Embeddings替代OpenAI Embeddings，增加网络配置
            self.llm_embeddings = GoogleGenerativeAIEmbeddings(
                model="embedding-001",
                google_api_key=google_api_key,
                request_timeout=120.0,  # 减少超时时间到120秒
                max_retries=3,  # 最大重试3次
                # transport="rest"  # 使用REST传输
            )

            logger.info("LLMParser初始化成功，已配置网络重试机制")

        except Exception as e:
            logger.error(f"LLMParser初始化失败: {str(e)}")
            # 提供降级方案：使用简单的文本解析
            self.llm = None
            self.llm_embeddings = None
            logger.warning("将使用降级的文本解析方案")

        self.vectorstore = None  # Will be initialized after document loading
        self.fallback_mode = self.llm is None  # 标记是否使用降级模式

    @staticmethod
    def _preprocess_template_string(template: str) -> str:
        """
        Preprocess the template string by removing leading whitespaces and indentation.
        Args:
            template (str): The template string to preprocess.
        Returns:
            str: The preprocessed template string.
        """
        return textwrap.dedent(template)
    
    def set_body_html(self, body_html):
        """
        Retrieves the job description from HTML, processes it, and initializes the vectorstore.
        Args:
            body_html (str): The HTML content to process.
        """
        # 保存原始HTML内容用于降级模式
        self.body_html = body_html

        # 如果在降级模式，直接返回，不进行向量化处理
        if self.fallback_mode:
            logger.info("使用降级模式，跳过向量化处理")
            return

        # Save the HTML content to a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".html", mode="w", encoding="utf-8") as temp_file:
            temp_file.write(body_html)
            temp_file_path = temp_file.name
        try:
            loader = TextLoader(temp_file_path, encoding="utf-8", autodetect_encoding=True)
            document = loader.load()
            logger.debug("Document successfully loaded.")
        except Exception as e:
            logger.error(f"Error during document loading: {e}")
            # 在错误情况下启用降级模式
            self.fallback_mode = True
            logger.warning("文档加载失败，启用降级模式")
            return
        finally:
            os.remove(temp_file_path)
            logger.debug(f"Temporary file removed: {temp_file_path}")

        # Split the text into chunks
        try:
            text_splitter = TokenTextSplitter(chunk_size=500, chunk_overlap=50)
            all_splits = text_splitter.split_documents(document)
            logger.debug(f"Text split into {len(all_splits)} fragments.")
        except Exception as e:
            logger.error(f"Error during text splitting: {e}")
            self.fallback_mode = True
            logger.warning("文本分割失败，启用降级模式")
            return

        # Create the vectorstore using FAISS
        try:
            if self.llm_embeddings:
                self.vectorstore = FAISS.from_documents(documents=all_splits, embedding=self.llm_embeddings)
                logger.debug("Vectorstore successfully initialized.")
            else:
                raise Exception("Embeddings not available")
        except Exception as e:
            logger.error(f"Error during vectorstore creation: {e}")
            self.fallback_mode = True
            logger.warning("向量存储创建失败，启用降级模式")

    def _retrieve_context(self, query: str, top_k: int = 3) -> str:
        """
        Retrieves the most relevant text fragments using the retriever.
        Args:
            query (str): The search query.
            top_k (int): Number of fragments to retrieve.
        Returns:
            str: Concatenated text fragments.
        """
        if not self.vectorstore:
            raise ValueError("Vectorstore not initialized. Run extract_job_description first.")
        
        retriever = self.vectorstore.as_retriever()
        retrieved_docs = retriever.get_relevant_documents(query)[:top_k]
        context = "\n\n".join(doc.page_content for doc in retrieved_docs)
        logger.debug(f"Context retrieved for query '{query}': {context[:200]}...")  # Log the first 200 characters
        return context
    
    def _extract_information(self, question: str, retrieval_query: str) -> str:
        """
        Generic method to extract specific information using the retriever and LLM.
        Args:
            question (str): The question to ask the LLM for extraction.
            retrieval_query (str): The query to use for retrieving relevant context.
        Returns:
            str: The extracted information.
        """
        # 如果在降级模式，使用简单的文本解析
        if self.fallback_mode:
            return self._extract_information_fallback(question, retrieval_query)

        try:
            context = self._retrieve_context(retrieval_query)

            prompt = ChatPromptTemplate.from_template(
                template="""
                You are an expert in extracting specific information from job descriptions.
                Carefully read the job description context below and provide a clear and concise answer to the question.

                Context: {context}

                Question: {question}
                Answer:
                """
            )

            formatted_prompt = prompt.format(context=context, question=question)
            logger.debug(f"Formatted prompt for extraction: {formatted_prompt[:200]}...")  # Log the first 200 characters

            chain = prompt | self.llm | StrOutputParser()
            result = chain.invoke({"context": context, "question": question})
            extracted_info = result.strip()
            logger.debug(f"Extracted information: {extracted_info}")
            return extracted_info
        except Exception as e:
            logger.error(f"Error during information extraction: {e}")
            logger.warning("LLM提取失败，切换到降级模式")
            self.fallback_mode = True
            return self._extract_information_fallback(question, retrieval_query)

    def _extract_information_fallback(self, question: str, retrieval_query: str) -> str:
        """
        降级模式：使用简单的文本解析提取信息
        Args:
            question (str): 要提取的信息类型
            retrieval_query (str): 检索查询
        Returns:
            str: 提取的信息
        """
        if not hasattr(self, 'body_html') or not self.body_html:
            logger.error("没有可用的HTML内容进行降级解析")
            return "信息提取失败"

        # 清理HTML标签，获取纯文本
        import re
        from html import unescape

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', ' ', self.body_html)
        # 解码HTML实体
        text = unescape(text)
        # 清理多余空白
        text = re.sub(r'\s+', ' ', text).strip()

        logger.debug(f"降级模式解析文本长度: {len(text)}")

        # 根据问题类型进行简单的文本匹配
        if "company" in question.lower() or "公司" in question:
            return self._extract_company_fallback(text)
        elif "role" in question.lower() or "title" in question.lower() or "职位" in question:
            return self._extract_role_fallback(text)
        elif "location" in question.lower() or "地点" in question:
            return self._extract_location_fallback(text)
        elif "description" in question.lower() or "描述" in question:
            return self._extract_description_fallback(text)
        else:
            return "无法识别的信息类型"
    
    def extract_job_description(self) -> str:
        """
        Extracts the company name from the job description.
        Returns:
            str: The extracted job description.
        """
        question = "What is the job description of the company?"
        retrieval_query = "Job description"
        logger.debug("Starting job description extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_company_name(self) -> str:
        """
        Extracts the company name from the job description.
        Returns:
            str: The extracted company name.
        """
        question = "What is the company's name?"
        retrieval_query = "Company name"
        logger.debug("Starting company name extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_role(self) -> str:
        """
        Extracts the sought role/title from the job description.
        Returns:
            str: The extracted role/title.
        """
        question = "What is the role or title sought in this job description?"
        retrieval_query = "Job title"
        logger.debug("Starting role/title extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_location(self) -> str:
        """
        Extracts the location from the job description.
        Returns:
            str: The extracted location.
        """
        question = "What is the location mentioned in this job description?"
        retrieval_query = "Location"
        logger.debug("Starting location extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_recruiter_email(self) -> str:
        """
        Extracts the recruiter's email from the job description.
        Returns:
            str: The extracted recruiter's email.
        """
        question = "What is the recruiter's email address in this job description?"
        retrieval_query = "Recruiter email"
        logger.debug("Starting recruiter email extraction.")
        email = self._extract_information(question, retrieval_query)
        
        # Validate the extracted email using regex
        email_regex = r'[\w\.-]+@[\w\.-]+\.\w+'
        if re.match(email_regex, email):
            logger.debug("Valid recruiter's email.")
            return email
        else:
            logger.warning("Invalid or not found recruiter's email.")
            return ""

    def _extract_company_fallback(self, text: str) -> str:
        """降级模式：提取公司名称"""
        import re

        # 常见的公司名称模式
        patterns = [
            r'(?:at|@|公司[:：]?)\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)\s*(?:is hiring|招聘|正在招聘)',
            r'Join\s+([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)',
            r'Company[:：]\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                company = matches[0].strip()
                if len(company) > 2 and not company.lower() in ['the', 'and', 'for', 'with']:
                    return company

        return "未知公司"

    def _extract_role_fallback(self, text: str) -> str:
        """降级模式：提取职位名称"""
        import re

        # 常见的职位标题模式
        patterns = [
            r'(?:Job Title|Position|Role|职位[:：]?)\s*[:：]?\s*([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)(?:\s|$|,|\.|;)',
            r'(?:We are looking for|Looking for|招聘)\s+(?:a|an)?\s*([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)\s*(?:position|role|job|职位)(?:\s|$|,|\.|;)',
            r'<title[^>]*>([^<]+)</title>'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                role = matches[0].strip()
                if len(role) > 2:
                    return role

        return "未知职位"

    def _extract_location_fallback(self, text: str) -> str:
        """降级模式：提取工作地点"""
        import re

        # 常见的地点模式
        patterns = [
            r'(?:Location|地点|Address|地址)[:：]?\s*([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)(?:\s|$|,|\.|;)',
            r'(?:Based in|Located in|位于)\s*([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)\s*(?:office|办公室|分公司)(?:\s|$|,|\.|;)'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                location = matches[0].strip()
                if len(location) > 2:
                    return location

        return "未知地点"

    def _extract_description_fallback(self, text: str) -> str:
        """降级模式：提取职位描述"""
        import re

        # 尝试找到职位描述部分
        desc_patterns = [
            r'(?:Job Description|Description|职位描述|工作描述)[:：]?\s*(.*?)(?:Requirements|要求|Qualifications|资格|$)',
            r'(?:About the role|关于职位|职位介绍)[:：]?\s*(.*?)(?:Requirements|要求|Qualifications|资格|$)',
            r'(?:Responsibilities|职责|工作内容)[:：]?\s*(.*?)(?:Requirements|要求|Qualifications|资格|$)'
        ]

        for pattern in desc_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                description = matches[0].strip()
                if len(description) > 50:  # 确保描述足够长
                    # 清理描述，限制长度
                    description = re.sub(r'\s+', ' ', description)
                    return description[:500] + "..." if len(description) > 500 else description

        # 如果没有找到特定的描述部分，返回文本的前500个字符
        if len(text) > 100:
            return text[:500] + "..."

        return "职位描述信息不完整"

