import os
import tempfile
import textwrap
import time
import re  # For email validation
from src.libs.resume_and_cover_builder.utils import LoggerChatModel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger
from pathlib import Path
from langchain_core.prompt_values import StringPromptValue
from langchain_core.runnables import RunnablePassthrough
from langchain_text_splitters import TokenTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS
from src.libs.resume_and_cover_builder.config import global_config
from langchain_community.document_loaders import TextLoader
from requests.exceptions import HTTPError as HTTPStatusError  # HTTP error handling
import config as cfg

# Load environment variables from the .env file
load_dotenv()

# Configure the log file
log_folder = 'log/resume/gpt_resume'
if not os.path.exists(log_folder):
    os.makedirs(log_folder)
log_path = Path(log_folder).resolve()
logger.add(log_path / "gpt_resume.log", rotation="1 day", compression="zip", retention="7 days", level="DEBUG")


class LLMParser:
    def __init__(self, openai_api_key=None, api_key=None):
        """
        初始化LLMParser，使用Gemini API
        Args:
            openai_api_key: 为了向后兼容保留的参数名
            api_key: Gemini API密钥
        """
        # 向后兼容处理
        if openai_api_key is not None:
            api_key = openai_api_key
        elif api_key is None:
            raise ValueError("必须提供API密钥")
        self.api_key = api_key

        try:
            # 使用Gemini模型，提高温度以增强创造性
            self.llm = LoggerChatModel(
                ChatGoogleGenerativeAI(
                    model="gemini-2.0-flash-exp",
                    google_api_key=api_key,
                    temperature=1.0,  # 使用最高允许温度以增强创造性和扩展能力
                    request_timeout=60.0,
                    max_retries=3,
                    transport="rest"  # 强制使用REST传输而不是gRPC
                )
            )
            # 暂时禁用embeddings，因为gRPC连接问题
            # self.llm_embeddings = GoogleGenerativeAIEmbeddings(
            #     model="models/embedding-001",
            #     google_api_key=api_key,
            #     request_timeout=60.0,
            #     max_retries=3,
            #     transport="rest"  # 强制使用REST传输而不是gRPC
            # )
            self.llm_embeddings = None  # 禁用embeddings
            logger.info("LLMParser初始化成功，使用Gemini模型 (REST传输)")
            self.fallback_mode = False

        except Exception as e:
            logger.error(f"LLMParser初始化失败: {str(e)}")
            logger.warning("启用降级模式，使用基础文本解析")
            self.llm = None
            self.llm_embeddings = None
            self.fallback_mode = True

        self.vectorstore = None  # Will be initialized after document loading

    @staticmethod
    def _preprocess_template_string(template: str) -> str:
        """
        Preprocess the template string by removing leading whitespaces and indentation.
        Args:
            template (str): The template string to preprocess.
        Returns:
            str: The preprocessed template string.
        """
        return textwrap.dedent(template)
    
    def set_body_html(self, body_html):
        """
        Retrieves the job description from HTML, processes it, and initializes the vectorstore.
        Args:
            body_html (str): The HTML content to process.
        """
        # 保存原始HTML内容用于降级模式
        self.body_html = body_html

        # 如果在降级模式，直接返回，不进行向量化处理
        if self.fallback_mode:
            logger.info("使用降级模式，跳过向量化处理")
            return

        # 暂时跳过向量化处理，因为embeddings的gRPC连接问题
        # 直接使用LLM进行解析，LLM已经配置为使用REST传输
        logger.info("跳过向量化处理，直接使用LLM解析（避免embeddings gRPC问题）")
        return

        # Save the HTML content to a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".html", mode="w", encoding="utf-8") as temp_file:
            temp_file.write(body_html)
            temp_file_path = temp_file.name
        try:
            loader = TextLoader(temp_file_path, encoding="utf-8", autodetect_encoding=True)
            document = loader.load()
            logger.debug("Document successfully loaded.")
        except Exception as e:
            logger.error(f"Error during document loading: {e}")
            # 在错误情况下启用降级模式
            self.fallback_mode = True
            logger.warning("文档加载失败，启用降级模式")
            return
        finally:
            os.remove(temp_file_path)
            logger.debug(f"Temporary file removed: {temp_file_path}")

        # Split the text into chunks
        try:
            text_splitter = TokenTextSplitter(chunk_size=500, chunk_overlap=50)
            all_splits = text_splitter.split_documents(document)
            logger.debug(f"Text split into {len(all_splits)} fragments.")
        except Exception as e:
            logger.error(f"Error during text splitting: {e}")
            self.fallback_mode = True
            logger.warning("文本分割失败，启用降级模式")
            return

        # Create the vectorstore using FAISS
        try:
            if self.llm_embeddings:
                self.vectorstore = FAISS.from_documents(documents=all_splits, embedding=self.llm_embeddings)
                logger.debug("Vectorstore successfully initialized.")
            else:
                raise Exception("Embeddings not available")
        except Exception as e:
            logger.error(f"Error during vectorstore creation: {e}")
            self.fallback_mode = True
            logger.warning("向量存储创建失败，启用降级模式")

    def _retrieve_context(self, query: str, top_k: int = 3) -> str:
        """
        Retrieves the most relevant text fragments using the retriever.
        Args:
            query (str): The search query.
            top_k (int): Number of fragments to retrieve.
        Returns:
            str: Concatenated text fragments.
        """
        # 由于跳过了向量化处理，直接使用HTML内容
        logger.debug(f"使用HTML内容作为上下文，查询: '{query}'")
        if hasattr(self, 'body_html') and self.body_html:
            # 清理HTML并限制内容长度以避免token限制
            import re
            from html import unescape

            # 移除HTML标签
            text = re.sub(r'<[^>]+>', ' ', self.body_html)
            # 解码HTML实体
            text = unescape(text)
            # 清理多余空白
            text = re.sub(r'\s+', ' ', text).strip()

            # 限制内容长度
            max_length = 6000  # 为LLM留出足够的token空间
            if len(text) > max_length:
                text = text[:max_length] + "..."

            logger.debug(f"上下文长度: {len(text)}")
            return text

        logger.warning("没有可用的HTML内容")
        return ""
    
    def _extract_information(self, question: str, retrieval_query: str) -> str:
        """
        Generic method to extract specific information using the retriever and LLM.
        Args:
            question (str): The question to ask the LLM for extraction.
            retrieval_query (str): The query to use for retrieving relevant context.
        Returns:
            str: The extracted information.
        """
        # 如果在降级模式，使用简单的文本解析
        if self.fallback_mode:
            return self._extract_information_fallback(question, retrieval_query)

        try:
            context = self._retrieve_context(retrieval_query)

            prompt = ChatPromptTemplate.from_template(
                template="""
                You are an expert in extracting specific information from job descriptions.
                Carefully read the job description context below and provide a clear and concise answer to the question.

                Context: {context}

                Question: {question}
                Answer:
                """
            )

            formatted_prompt = prompt.format(context=context, question=question)
            logger.debug(f"Formatted prompt for extraction: {formatted_prompt[:200]}...")  # Log the first 200 characters

            chain = prompt | self.llm | StrOutputParser()
            result = chain.invoke({"context": context, "question": question})
            extracted_info = result.strip()
            logger.debug(f"Extracted information: {extracted_info}")
            return extracted_info
        except Exception as e:
            logger.error(f"Error during information extraction: {e}")
            logger.warning("LLM提取失败，切换到降级模式")
            self.fallback_mode = True
            return self._extract_information_fallback(question, retrieval_query)

    def _extract_information_fallback(self, question: str, retrieval_query: str) -> str:
        """
        降级模式：使用简单的文本解析提取信息
        Args:
            question (str): 要提取的信息类型
            retrieval_query (str): 检索查询
        Returns:
            str: 提取的信息
        """
        if not hasattr(self, 'body_html') or not self.body_html:
            logger.error("没有可用的HTML内容进行降级解析")
            return "信息提取失败"

        # 清理HTML标签，获取纯文本
        import re
        from html import unescape

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', ' ', self.body_html)
        # 解码HTML实体
        text = unescape(text)
        # 清理多余空白
        text = re.sub(r'\s+', ' ', text).strip()

        logger.debug(f"降级模式解析文本长度: {len(text)}")

        # 根据问题类型进行简单的文本匹配
        if "company" in question.lower() or "公司" in question:
            return self._extract_company_fallback(text)
        elif "role" in question.lower() or "title" in question.lower() or "职位" in question:
            return self._extract_role_fallback(text)
        elif "location" in question.lower() or "地点" in question:
            return self._extract_location_fallback(text)
        elif "description" in question.lower() or "描述" in question:
            return self._extract_description_fallback(text)
        else:
            return "无法识别的信息类型"
    
    def extract_job_description(self) -> str:
        """
        Extracts the detailed job description including responsibilities, requirements, and qualifications.
        Returns:
            str: The extracted job description with full details.
        """
        question = """Please extract and organize the job information in a clean, simple text format. DO NOT use any markdown formatting like **, *, or ###. Use plain text only.

        Structure the output as follows:

        [Company Name] is seeking a [Job Title] for [Location]. [Brief 1-2 sentence overview of the role and company context.]

        Job Responsibilities:
        • [List main duties and responsibilities using simple bullet points]

        Required Qualifications:
        • [List required skills, experience, and qualifications using simple bullet points]

        Preferred Qualifications:
        • [List preferred but not required qualifications using simple bullet points]

        Company Information:
        • [Brief company background and any mentioned benefits using simple bullet points]

        IMPORTANT: Use only plain text with simple bullet points (•). Do not use any bold (**), italic (*), or heading (###) markdown formatting. Keep the text clean, simple, and professional."""
        retrieval_query = "Job description responsibilities requirements qualifications"
        logger.debug("Starting comprehensive job description extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_company_name(self) -> str:
        """
        Extracts the company name from the job description.
        Returns:
            str: The extracted company name.
        """
        question = "What is the company's name?"
        retrieval_query = "Company name"
        logger.debug("Starting company name extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_role(self) -> str:
        """
        Extracts the sought role/title from the job description.
        Returns:
            str: The extracted role/title.
        """
        question = "What is the role or title sought in this job description?"
        retrieval_query = "Job title"
        logger.debug("Starting role/title extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_location(self) -> str:
        """
        Extracts the location from the job description.
        Returns:
            str: The extracted location.
        """
        question = "What is the location mentioned in this job description?"
        retrieval_query = "Location"
        logger.debug("Starting location extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_recruiter_email(self) -> str:
        """
        Extracts the recruiter's email from the job description.
        Returns:
            str: The extracted recruiter's email.
        """
        question = "What is the recruiter's email address in this job description?"
        retrieval_query = "Recruiter email"
        logger.debug("Starting recruiter email extraction.")
        email = self._extract_information(question, retrieval_query)
        
        # Validate the extracted email using regex
        email_regex = r'[\w\.-]+@[\w\.-]+\.\w+'
        if re.match(email_regex, email):
            logger.debug("Valid recruiter's email.")
            return email
        else:
            logger.warning("Invalid or not found recruiter's email.")
            return ""

    def _extract_company_fallback(self, text: str) -> str:
        """降级模式：提取公司名称"""
        import re

        # 常见的公司名称模式
        patterns = [
            r'<title[^>]*>.*?-\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)\s*</title>',  # 从标题提取
            r'Company[:：]\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)',
            r'(?:at|@|公司[:：]?)\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)\s*(?:is hiring|招聘|正在招聘)',
            r'Join\s+([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)',
            r'team at\s+([A-Za-z][A-Za-z0-9\s&.,\-]{2,50}?)(?:\s|$|,|\.|;)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                company = matches[0].strip()
                # 过滤掉常见的无效词汇
                invalid_words = ['the', 'and', 'for', 'with', 'our', 'team', 'role', 'position', 'job', 'work']
                if len(company) > 2 and company.lower() not in invalid_words:
                    return company

        return "未知公司"

    def _extract_role_fallback(self, text: str) -> str:
        """降级模式：提取职位名称"""
        import re

        # 常见的职位标题模式
        patterns = [
            r'<title[^>]*>([^<\-]+?)(?:\s*-.*)?</title>',  # 从标题提取，去掉公司部分
            r'<h1[^>]*>([^<]+)</h1>',  # 从h1标签提取
            r'(?:Job Title|Position|Role|职位[:：]?)\s*[:：]?\s*([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)(?:\s|$|,|\.|;)',
            r'(?:We are looking for|Looking for|招聘)\s+(?:a|an)?\s*([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)\s*(?:position|role|job|职位)(?:\s|$|,|\.|;)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                role = matches[0].strip()
                # 清理常见的无效词汇
                invalid_words = ['the', 'and', 'for', 'with', 'our', 'team', 'company', 'at']
                if len(role) > 2 and role.lower() not in invalid_words:
                    return role

        return "未知职位"

    def _extract_location_fallback(self, text: str) -> str:
        """降级模式：提取工作地点"""
        import re

        # 常见的地点模式
        patterns = [
            r'Location[:：]?\s*([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)(?:\s*</|$|,|\.|;)',
            r'(?:Based in|Located in|位于)\s*([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)\s*(?:office|办公室|分公司)(?:\s|$|,|\.|;)',
            r'([A-Z][a-z]+,\s*[A-Z]{2})',  # 匹配 "Boston, MA" 格式
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)',  # 匹配 "New York" 格式
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                location = matches[0].strip()
                # 过滤掉常见的无效词汇
                invalid_words = ['company', 'team', 'role', 'position', 'job', 'work', 'the', 'and']
                if len(location) > 2 and location.lower() not in invalid_words:
                    return location

        return "未知地点"

    def _extract_description_fallback(self, text: str) -> str:
        """降级模式：提取职位描述"""
        import re

        # 清理HTML标签
        clean_text = re.sub(r'<[^>]+>', ' ', text)
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()

        # 尝试找到职位描述部分 - 更全面的模式
        desc_patterns = [
            # 英文模式
            r'(?:Job Description|Description|About this role|About the role|Role Overview|Position Summary)[:：]?\s*(.*?)(?:Requirements|Qualifications|Skills|Experience|What we|Benefits|Apply|$)',
            r'(?:Responsibilities|Key Responsibilities|Main Responsibilities|Your responsibilities)[:：]?\s*(.*?)(?:Requirements|Qualifications|Skills|Experience|What we|Benefits|Apply|$)',
            r'(?:What you.ll do|What you will do|Your role|In this role)[:：]?\s*(.*?)(?:Requirements|Qualifications|Skills|Experience|What we|Benefits|Apply|$)',
            # 中文模式
            r'(?:职位描述|工作描述|岗位职责|工作职责|职责描述|岗位要求)[:：]?\s*(.*?)(?:任职要求|技能要求|工作要求|福利待遇|申请|$)',
            r'(?:关于职位|职位介绍|岗位介绍)[:：]?\s*(.*?)(?:任职要求|技能要求|工作要求|福利待遇|申请|$)',
        ]

        best_description = ""
        max_length = 0

        for pattern in desc_patterns:
            matches = re.findall(pattern, clean_text, re.IGNORECASE | re.DOTALL)
            if matches:
                for match in matches:
                    description = match.strip()
                    if len(description) > max_length and len(description) > 50:
                        best_description = description
                        max_length = len(description)

        if best_description:
            # 清理描述，限制长度，并简化格式
            best_description = re.sub(r'\s+', ' ', best_description)
            # 移除过多的markdown格式
            best_description = re.sub(r'\*\*([^*]+)\*\*', r'\1', best_description)  # 移除粗体
            best_description = re.sub(r'\*([^*]+)\*', r'\1', best_description)      # 移除斜体
            best_description = re.sub(r'#+\s*', '', best_description)               # 移除标题符号
            return best_description[:1000] + "..." if len(best_description) > 1000 else best_description

        # 如果没有找到特定的描述部分，尝试提取主要内容
        # 查找包含关键词的段落
        keywords = ['responsibilities', 'requirements', 'qualifications', 'experience', 'skills',
                   'role', 'position', '职责', '要求', '经验', '技能', '岗位']

        sentences = clean_text.split('.')
        relevant_sentences = []

        for sentence in sentences:
            if any(keyword.lower() in sentence.lower() for keyword in keywords):
                relevant_sentences.append(sentence.strip())

        if relevant_sentences:
            description = '. '.join(relevant_sentences[:5])  # 取前5个相关句子
            return description[:1000] + "..." if len(description) > 1000 else description

        # 最后的降级：返回文本的前1000个字符
        if len(clean_text) > 100:
            return clean_text[:1000] + "..."

        return "职位描述信息不完整"

    def parse_resume_content(self, resume_text: str) -> dict:
        """
        解析简历内容，提取关键信息
        """
        try:
            prompt = f"""
            请分析以下简历内容，提取关键信息并以JSON格式返回：

            简历内容：
            {resume_text}

            请提取以下信息：
            1. 个人信息 (personal_info):
               - 姓名 (name)
               - 邮箱 (email)
               - 电话 (phone)
               - 地址 (address)
               - LinkedIn (linkedin)
               - GitHub (github)
            2. 教育背景 (education) - 数组格式，每项包含：
               - 学校名称 (school)
               - 专业 (major)
               - 学位 (degree)
               - 毕业时间 (graduation_date)
               - GPA (gpa)
            3. 工作经验 (work_experience) - 数组格式，每项包含：
               - 公司名称 (company)
               - 职位 (position)
               - 工作时间 (duration)
               - 工作描述 (description)
               - 主要成就 (achievements)
            4. 技能 (skills) - 数组格式，分类：
               - 编程语言 (programming_languages)
               - 技术框架 (frameworks)
               - 工具软件 (tools)
               - 软技能 (soft_skills)
            5. 项目经验 (projects) - 数组格式，每项包含：
               - 项目名称 (name)
               - 项目描述 (description)
               - 使用技术 (technologies)
               - 项目时间 (duration)
            6. 证书资质 (certifications) - 数组格式
            7. 语言能力 (languages) - 数组格式

            请确保返回有效的JSON格式，如果某些信息无法提取，请使用null或空数组。
            """

            # 使用正确的调用方法
            if hasattr(self.llm, 'invoke'):
                response = self.llm.invoke(prompt)
            elif hasattr(self.llm, 'predict'):
                response = self.llm.predict(prompt)
            else:
                # 如果是LoggerChatModel包装的，尝试访问内部模型
                if hasattr(self.llm, 'llm') and hasattr(self.llm.llm, 'invoke'):
                    response = self.llm.llm.invoke(prompt)
                else:
                    raise Exception("无法调用LLM模型")

            # 尝试解析JSON响应
            import json
            try:
                # 提取JSON部分
                content = response.content if hasattr(response, 'content') else str(response)

                # 查找JSON块
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    parsed_data = json.loads(json_str)
                    return parsed_data
                else:
                    # 如果没有找到JSON，返回基本结构
                    return self._get_empty_resume_structure()
            except json.JSONDecodeError:
                # JSON解析失败，返回基本结构
                return self._get_empty_resume_structure()

        except Exception as e:
            print(f"简历内容解析错误: {e}")
            return self._get_empty_resume_structure()

    def _get_empty_resume_structure(self) -> dict:
        """返回空的简历数据结构"""
        return {
            "personal_info": {
                "name": None,
                "email": None,
                "phone": None,
                "address": None,
                "linkedin": None,
                "github": None
            },
            "education": [],
            "work_experience": [],
            "skills": {
                "programming_languages": [],
                "frameworks": [],
                "tools": [],
                "soft_skills": []
            },
            "projects": [],
            "certifications": [],
            "languages": []
        }

