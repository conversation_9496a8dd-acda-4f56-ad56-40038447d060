from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import yaml
import os
from pathlib import Path
import tempfile
import PyPDF2
from io import BytesIO
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from loguru import logger
import sys

# Add the project root to the Python path for absolute imports from src
project_root = Path(__file__).resolve().parents[2] # d:\Jobs_Applier_AI_Agent_AIHawk-main
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

DATA_FOLDER = project_root / "data_folder"

# Now imports from 'src.' should work as 'src' is a top-level package in the project_root
from src.libs.resume_and_cover_builder.resume_facade import ResumeFacade
from src.libs.resume_and_cover_builder.style_manager import StyleManager
from src.libs.resume_and_cover_builder.resume_generator import ResumeGenerator
from src.resume_schemas.resume import Resume
from src.utils.chrome_utils import init_browser # Added import
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
import config

global_config = None
try:
    from src.libs.resume_and_cover_builder.config import global_config
except ImportError:
    try:
        from src.libs.resume_and_cover_builder import config as config_module
        global_config = getattr(config_module, 'global_config', None)
    except Exception:
        pass


# Import LinkedIn automation API
from webui.backend.linkedin_api import router as linkedin_router

app = FastAPI(title="AIHawk Job Application Assistant", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include LinkedIn automation router
app.include_router(linkedin_router)

# 从secrets.yaml获取API密钥
def load_api_key():
    try:
        secrets_path = DATA_FOLDER / "secrets.yaml"
        with open(secrets_path, 'r', encoding='utf-8') as file:
            secrets = yaml.safe_load(file)
            api_key = secrets.get('llm_api_key', '')
            if not api_key:
                print("警告: API密钥未在secrets.yaml中找到")
                return ''
            print("成功: API密钥已从secrets.yaml加载")
            return api_key
    except FileNotFoundError:
        print(f"错误: 未找到secrets.yaml文件，请确保文件存在于{str(DATA_FOLDER)}目录中")
        return ''
    except Exception as e:
        print(f"错误: 加载API密钥时发生异常: {e}")
        return ''

# 加载简历对象
def load_resume_object():
    try:
        resume_path = DATA_FOLDER / "plain_text_resume.yaml"
        with open(resume_path, 'r', encoding='utf-8') as file:
            yaml_content = file.read()
            return Resume(yaml_content)
    except Exception as e:
        print(f"Error loading resume object: {e}")
        return None

# 初始化核心组件
api_key = load_api_key()
style_manager = StyleManager()
resume_generator = ResumeGenerator()
resume_object = load_resume_object()

resume_facade = ResumeFacade(
    api_key=api_key,
    style_manager=style_manager,
    resume_generator=resume_generator,
    resume_object=resume_object,
    output_path=str(DATA_FOLDER / "output")
)

# 设置 Selenium driver
# chrome_options = webdriver.ChromeOptions()
# chrome_options.add_argument('--headless')
# driver = webdriver.Chrome(executable_path="C:/Program Files/chromedriver-win64/chromedriver.exe", options=chrome_options)
# resume_facade.set_driver(driver)

try:
    logger.info("Initializing WebDriver...")
    driver = init_browser(headless=True)  # Use init_browser from chrome_utils
    if driver:
        resume_facade.set_driver(driver)
        logger.info("WebDriver initialized and set successfully.")
    else:
        logger.error("Failed to initialize WebDriver. Driver is None.")
        # Decide how to handle this: exit, or run without Selenium features?
        # For now, we'll log the error and continue, some features might not work.
except Exception as e:
    logger.error(f"Error initializing WebDriver: {e}", exc_info=True)
    # Decide how to handle this, for now, log and continue.


@app.get("/api/health")
def health_check():
    return {"status": "ok"}

def generate_optimized_resume_content(user_resume_data, job_info):
    """
    根据用户简历和职位信息生成优化的简历内容
    """
    try:
        api_key = global_config.API_KEY
        if not api_key:
            raise Exception("API密钥未配置")

        from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
        llm_parser = LLMParser(api_key=api_key)

        # 提取用户信息
        personal_info = user_resume_data.get('personal_info', {})
        name = personal_info.get('name') or '[您的姓名]'
        email = personal_info.get('email') or '[您的邮箱]'
        phone = personal_info.get('phone') or '[您的电话]'
        address = personal_info.get('address') or '[您的地址]'

        # 构建优化提示
        optimization_prompt = f"""
        根据以下信息生成一份专业的HTML简历，针对目标职位进行优化。

        目标职位：{job_info.get('role', '未知职位')} - {job_info.get('company', '未知公司')}

        用户信息：
        姓名：{name}
        邮箱：{email}
        电话：{phone}
        地址：{address}

        完整简历数据：
        {user_resume_data}

        重要要求：
        1. 直接返回完整的HTML代码，不要任何说明文字
        2. 使用用户的真实信息
        3. **严格保留所有原始工作经历**：
           - 保持所有公司名称完全一致
           - 保持所有工作时间段完全一致
           - 保持所有职位名称完全一致
           - 保持所有工作描述的核心内容
        4. **针对性优化策略**：
           - 仅对与目标职位相关的工作经历进行描述优化
           - 突出相关技能和成就的表述方式
           - 调整关键词以匹配目标职位要求
           - 重新排列工作描述的重点，但不改变事实内容
        5. **绝对禁止**：
           - 添加不存在的工作经历
           - 编造技能或经验
           - 修改公司名称、时间或职位标题
           - 夸大或虚构成就
        6. 专业美观的格式

        请直接返回HTML代码，格式如下：

        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{name} - 简历</title>
            <style>
                * {{ box-sizing: border-box; }}
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    line-height: 1.6;
                }}
                .resume {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .name {{
                    font-size: 2.2em;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                    word-break: break-word;
                }}
                .contact {{
                    color: #7f8c8d;
                    font-size: 1em;
                    word-break: break-all;
                }}
                .section {{
                    margin-bottom: 25px;
                    clear: both;
                }}
                .section-title {{
                    font-size: 1.4em;
                    font-weight: bold;
                    color: #2c3e50;
                    border-bottom: 1px solid #bdc3c7;
                    padding-bottom: 5px;
                    margin-bottom: 15px;
                }}
                .job {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .job-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .job-title {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .company {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                .description {{
                    margin-top: 8px;
                    text-align: justify;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .skills {{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                }}
                .skill {{
                    background: #3498db;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 0.85em;
                    white-space: nowrap;
                }}
                @media (max-width: 600px) {{
                    .resume {{ padding: 20px; }}
                    .name {{ font-size: 1.8em; }}
                    .contact {{ font-size: 0.9em; }}
                    .job-header {{ flex-direction: column; }}
                    .date {{ margin-left: 0; margin-top: 5px; }}
                }}
            </style>
        </head>
        <body>
            <div class="resume">
                <div class="header">
                    <div class="name">{name}</div>
                    <div class="contact">{phone} | {email} | {address}</div>
                </div>
                <!-- 其他简历内容 -->
            </div>
        </body>
        </html>
        """

        # 使用LLM生成优化简历
        if hasattr(llm_parser.llm, 'invoke'):
            response = llm_parser.llm.invoke(optimization_prompt)
        elif hasattr(llm_parser.llm, 'predict'):
            response = llm_parser.llm.predict(optimization_prompt)
        elif hasattr(llm_parser.llm, 'llm') and hasattr(llm_parser.llm.llm, 'invoke'):
            response = llm_parser.llm.llm.invoke(optimization_prompt)
        else:
            raise Exception("无法调用LLM模型")

        # 提取响应内容
        if hasattr(response, 'content'):
            optimized_content = response.content
        else:
            optimized_content = str(response)

        # 清理HTML内容
        cleaned_content = clean_html_content(optimized_content)

        return cleaned_content

    except Exception as e:
        logger.error(f"简历优化失败: {str(e)}")
        raise e

def clean_html_content(html_content):
    """
    清理LLM生成的HTML内容，去除多余的说明文字
    """
    import re

    # 去除开头的说明文字
    patterns_to_remove = [
        r'^.*?(?=<!DOCTYPE|<html)',  # 去除HTML开始前的所有文字
        r'```html\s*',  # 去除```html标记
        r'```\s*$',     # 去除结尾的```标记
        r'\*\*.*?\*\*',  # 去除**包围的文字
        r'CSS.*?(?=</style>)',  # 去除CSS说明文字
        r'```css.*?```',  # 去除CSS代码块
    ]

    cleaned = html_content
    for pattern in patterns_to_remove:
        cleaned = re.sub(pattern, '', cleaned, flags=re.DOTALL | re.MULTILINE)

    # 确保HTML结构完整
    if not cleaned.strip().startswith('<!DOCTYPE') and not cleaned.strip().startswith('<html'):
        # 如果没有完整的HTML结构，提取HTML部分
        html_match = re.search(r'<!DOCTYPE.*?</html>', cleaned, re.DOTALL)
        if html_match:
            cleaned = html_match.group(0)

    return cleaned.strip()

def validate_api_key():
    """验证API密钥的有效性和类型"""
    if not global_config.API_KEY:
        print("错误: API密钥未配置")
        return False, "API密钥未配置，请检查secrets.yaml文件"
    
    # 检查API密钥格式（Gemini API密钥通常以'AIza'开头）
    if not global_config.API_KEY.startswith('AIza'):
        print("错误: API密钥格式不正确，请确保使用有效的Gemini API密钥")
        return False, "API密钥格式不正确，请确保使用有效的Gemini API密钥"
    
    return True, ""

class ResumeGenerateRequest(BaseModel):
    job_url: str
    user_resume: Optional[dict] = None

@app.post("/api/resume/upload")
async def upload_resume(resume: UploadFile = File(...)):
    """
    上传并解析用户简历
    """
    try:
        # 验证文件类型
        if resume.content_type != "application/pdf":
            return {"status": "error", "message": "只支持PDF格式的简历文件"}

        # 读取文件内容
        content = await resume.read()

        # 解析PDF内容
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(content))
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"

            if not text_content.strip():
                return {"status": "error", "message": "无法从PDF中提取文本内容"}

            # 使用LLM解析简历内容
            from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
            llm_parser = LLMParser(api_key=api_key)
            resume_data = llm_parser.parse_resume_content(text_content)

            return {
                "status": "success",
                "message": "简历上传并解析成功",
                "resume_data": resume_data
            }

        except Exception as parse_error:
            return {"status": "error", "message": f"简历解析失败: {str(parse_error)}"}

    except Exception as e:
        return {"status": "error", "message": f"文件上传失败: {str(e)}"}

@app.post("/api/resume/generate")
async def generate_resume(request: ResumeGenerateRequest):
    job_url = request.job_url
    user_resume = request.user_resume

    if not job_url or not job_url.startswith('http'):
        return {"status": "error", "message": "无效的职位URL，请提供有效的网址"}

    try:
        # 检查组件初始化状态
        if not resume_facade.resume_generator or not resume_facade.style_manager:
            print("错误: 系统组件未正确初始化")
            return {"status": "error", "message": "系统组件未正确初始化，请检查系统配置"}

        # 验证API密钥
        is_valid, error_message = validate_api_key()
        if not is_valid:
            print(f"API密钥验证失败: {error_message}")
            return {"status": "warning", "message": f"API密钥问题: {error_message}，将使用降级模式解析"}

        # 如果用户上传了简历，设置用户简历数据
        if user_resume:
            print("检测到用户上传的简历，将生成针对性优化简历")
            resume_facade.set_user_resume(user_resume)

        # 调用原有逻辑
        print(f"开始处理职位URL: {job_url}")

        try:
            resume_facade.link_to_job(job_url)

            # 检查是否成功解析到职位信息
            if hasattr(resume_facade, 'job') and resume_facade.job:
                job_info = {
                    "role": resume_facade.job.role or "未知职位",
                    "company": resume_facade.job.company or "未知公司",
                    "location": resume_facade.job.location or "未知地点"
                }

                # 检查是否使用了降级模式
                fallback_used = False
                if hasattr(resume_facade, 'llm_job_parser') and hasattr(resume_facade.llm_job_parser, 'fallback_mode'):
                    fallback_used = resume_facade.llm_job_parser.fallback_mode

                success_message = "职位信息已成功解析"
                if fallback_used:
                    success_message += "（使用降级模式）"

                print(f"成功解析职位信息: {job_info}")

                # 如果有用户简历数据，生成优化的简历内容
                optimized_resume = None
                if user_resume:
                    try:
                        optimized_resume = generate_optimized_resume_content(user_resume, job_info)
                        print("成功生成优化简历内容")
                    except Exception as e:
                        print(f"简历优化失败: {str(e)}")

                return {
                    "status": "success",
                    "message": success_message,
                    "job_info": job_info,
                    "fallback_mode": fallback_used,
                    "optimized_resume": optimized_resume
                }
            else:
                return {"status": "error", "message": "无法解析职位信息，请检查URL是否有效"}

        except Exception as parse_error:
            print(f"职位解析错误: {str(parse_error)}")
            # 尝试提供基本的错误恢复
            return {
                "status": "error",
                "message": f"职位解析失败: {str(parse_error)}。可能的原因：网络连接问题、页面结构变化或API服务不可用。"
            }

    except AttributeError as e:
        error_msg = f"组件属性错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}
    except ConnectionError as e:
        error_msg = f"网络连接错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": f"网络连接失败: {error_msg}。请检查网络连接或稍后重试。"}
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": f"系统错误: {error_msg}。请稍后重试或联系技术支持。"}

@app.post("/api/resume/generate-pdf")
async def generate_resume_pdf(request: dict):
    """
    生成PDF简历
    Args:
        request: 包含style和optimized_resume_html的请求数据
    Returns:
        PDF文件的base64编码
    """
    try:
        style = request.get('style', 'modern')
        optimized_resume_html = request.get('optimized_resume_html')

        if optimized_resume_html:
            # 使用优化后的HTML内容生成PDF
            return await generate_pdf_from_html(optimized_resume_html, style)
        else:
            # 使用原有逻辑
            return await generate_pdf_original_method(style)

    except Exception as e:
        error_msg = f"PDF生成失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}

async def generate_pdf_from_html(html_content, style):
    """
    从HTML内容生成PDF
    """
    try:
        import tempfile
        import os
        import base64
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        import time

        # 清理HTML内容，提取实际的HTML代码
        if '```html' in html_content:
            # 提取HTML代码块
            start = html_content.find('```html') + 7
            end = html_content.find('```', start)
            if end != -1:
                html_content = html_content[start:end].strip()

        # 创建临时HTML文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            temp_html_path = f.name

        try:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--print-to-pdf')

            # 初始化WebDriver
            from src.utils.chrome_utils import init_browser
            driver = init_browser()

            # 加载HTML文件
            file_url = f"file:///{temp_html_path.replace(os.sep, '/')}"
            driver.get(file_url)
            time.sleep(2)  # 等待页面加载

            # 生成PDF
            pdf_data = driver.execute_cdp_cmd('Page.printToPDF', {
                'format': 'A4',
                'printBackground': True,
                'marginTop': 0.4,
                'marginBottom': 0.4,
                'marginLeft': 0.4,
                'marginRight': 0.4
            })

            driver.quit()

            # 获取PDF的base64数据
            pdf_base64 = pdf_data['data']

            # 生成文件名
            import uuid
            filename = str(uuid.uuid4())[:8]

            return {
                "status": "success",
                "message": "PDF简历生成成功",
                "pdf_data": pdf_base64,
                "filename": f"optimized_resume_{filename}.pdf"
            }

        finally:
            # 清理临时文件
            if os.path.exists(temp_html_path):
                os.unlink(temp_html_path)

    except Exception as e:
        error_msg = f"HTML转PDF失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}

async def generate_pdf_original_method(style):
    """
    使用原有方法生成PDF
    """
    try:
        # 检查组件初始化状态
        if not resume_facade.resume_generator or not resume_facade.style_manager:
            return {"status": "error", "message": "系统组件未正确初始化"}

        # 检查是否已经解析了职位信息
        if not hasattr(resume_facade, 'job') or not resume_facade.job:
            return {"status": "error", "message": "请先解析职位信息"}

        # 设置样式
        try:
            resume_facade.style_manager.set_selected_style(style)
            print(f"设置简历样式: {style}")
        except Exception as e:
            print(f"样式设置失败，使用默认样式: {str(e)}")
            # 使用默认样式
            available_styles = resume_facade.style_manager.get_styles()
            if available_styles:
                default_style = list(available_styles.keys())[0]
                resume_facade.style_manager.set_selected_style(default_style)
                print(f"使用默认样式: {default_style}")

        # 初始化浏览器驱动
        from src.utils.chrome_utils import init_browser
        driver = init_browser()
        resume_facade.set_driver(driver)

        # 生成PDF简历
        print("开始生成PDF简历...")
        pdf_base64, suggested_name = resume_facade.create_resume_pdf_job_tailored()

        print(f"PDF简历生成成功，文件名: {suggested_name}")

        return {
            "status": "success",
            "message": "PDF简历生成成功",
            "pdf_data": pdf_base64,
            "filename": f"resume_{suggested_name}.pdf"
        }

    except Exception as e:
        error_msg = f"PDF生成失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}

@app.get("/api/resume/styles")
async def get_resume_styles():
    """
    获取可用的简历样式列表
    """
    try:
        styles = resume_facade.style_manager.get_styles()
        style_list = []
        for style_name, (file_name, author_link) in styles.items():
            style_list.append({
                "name": style_name,
                "file_name": file_name,
                "author_link": author_link
            })

        return {
            "status": "success",
            "styles": style_list
        }
    except Exception as e:
        return {"status": "error", "message": f"获取样式列表失败: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    print("启动 FastAPI 服务器...")
    print("LinkedIn API 可在 http://localhost:8002/api/linkedin/ 访问")
    print("简历生成 API 可在 http://localhost:8002/api/resume/ 访问")
    print("健康检查 API 可在 http://localhost:8002/api/health 访问")
    uvicorn.run(app, host="0.0.0.0", port=8002)