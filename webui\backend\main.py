from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import yaml
import os
from pathlib import Path
import tempfile
import PyPDF2
from io import BytesIO
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from loguru import logger
import sys

# Add the project root to the Python path for absolute imports from src
project_root = Path(__file__).resolve().parents[2] # d:\Jobs_Applier_AI_Agent_AIHawk-main
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

DATA_FOLDER = project_root / "data_folder"

# Now imports from 'src.' should work as 'src' is a top-level package in the project_root
from src.libs.resume_and_cover_builder.resume_facade import ResumeFacade
from src.libs.resume_and_cover_builder.style_manager import StyleManager
from src.libs.resume_and_cover_builder.resume_generator import ResumeGenerator
from src.resume_schemas.resume import Resume
from src.utils.chrome_utils import init_browser # Added import
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
import config

global_config = None
try:
    from src.libs.resume_and_cover_builder.config import global_config
except ImportError:
    try:
        from src.libs.resume_and_cover_builder import config as config_module
        global_config = getattr(config_module, 'global_config', None)
    except Exception:
        pass


# Import LinkedIn automation API
from webui.backend.linkedin_api import router as linkedin_router

app = FastAPI(title="AIHawk Job Application Assistant", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include LinkedIn automation router
app.include_router(linkedin_router)

# 从secrets.yaml获取API密钥
def load_api_key():
    try:
        secrets_path = DATA_FOLDER / "secrets.yaml"
        with open(secrets_path, 'r', encoding='utf-8') as file:
            secrets = yaml.safe_load(file)
            api_key = secrets.get('llm_api_key', '')
            if not api_key:
                print("警告: API密钥未在secrets.yaml中找到")
                return ''
            print("成功: API密钥已从secrets.yaml加载")
            return api_key
    except FileNotFoundError:
        print(f"错误: 未找到secrets.yaml文件，请确保文件存在于{str(DATA_FOLDER)}目录中")
        return ''
    except Exception as e:
        print(f"错误: 加载API密钥时发生异常: {e}")
        return ''

# 加载简历对象
def load_resume_object():
    try:
        resume_path = DATA_FOLDER / "plain_text_resume.yaml"
        with open(resume_path, 'r', encoding='utf-8') as file:
            yaml_content = file.read()
            return Resume(yaml_content)
    except Exception as e:
        print(f"Error loading resume object: {e}")
        return None

# 初始化核心组件
api_key = load_api_key()
style_manager = StyleManager()
resume_generator = ResumeGenerator()
resume_object = load_resume_object()

resume_facade = ResumeFacade(
    api_key=api_key,
    style_manager=style_manager,
    resume_generator=resume_generator,
    resume_object=resume_object,
    output_path=str(DATA_FOLDER / "output")
)

# 设置 Selenium driver
# chrome_options = webdriver.ChromeOptions()
# chrome_options.add_argument('--headless')
# driver = webdriver.Chrome(executable_path="C:/Program Files/chromedriver-win64/chromedriver.exe", options=chrome_options)
# resume_facade.set_driver(driver)

try:
    logger.info("Initializing WebDriver...")
    driver = init_browser(headless=True)  # Use init_browser from chrome_utils
    if driver:
        resume_facade.set_driver(driver)
        logger.info("WebDriver initialized and set successfully.")
    else:
        logger.error("Failed to initialize WebDriver. Driver is None.")
        # Decide how to handle this: exit, or run without Selenium features?
        # For now, we'll log the error and continue, some features might not work.
except Exception as e:
    logger.error(f"Error initializing WebDriver: {e}", exc_info=True)
    # Decide how to handle this, for now, log and continue.


@app.get("/api/health")
def health_check():
    return {"status": "ok"}

def generate_optimized_resume_content(user_resume_data, job_info):
    """
    根据用户简历和职位信息生成优化的简历内容
    """
    try:
        api_key = global_config.API_KEY
        if not api_key:
            raise Exception("API密钥未配置")

        from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
        llm_parser = LLMParser(api_key=api_key)

        # 提取用户信息
        personal_info = user_resume_data.get('personal_info', {})
        name = personal_info.get('name') or '[您的姓名]'
        email = personal_info.get('email') or '[您的邮箱]'
        phone = personal_info.get('phone') or '[您的电话]'
        address = personal_info.get('address') or '[您的地址]'

        # 构建优化提示
        optimization_prompt = f"""
        根据以下信息生成一份专业的HTML简历，针对目标职位进行优化。

        目标职位：{job_info.get('role', '未知职位')} - {job_info.get('company', '未知公司')}
        职位地点：{job_info.get('location', '未知地点')}

        用户信息：
        姓名：{name}
        邮箱：{email}
        电话：{phone}
        地址：{address}

        完整简历数据：
        {user_resume_data}

        **职位分析要求**：
        请仔细分析目标职位的核心要求，包括：
        - 所需的关键技能和经验（技术技能、软技能、行业知识等）
        - 行业背景要求（相关行业经验、专业知识等）
        - 职位层级要求（管理经验、领导能力、执行能力等）
        - 公司文化和价值观匹配点
        - 教育背景和认证要求
        - 语言能力和国际化经验（如适用）

        **核心使命**：将用户的简历转化为一份令人印象深刻、充满说服力的专业文档，让招聘者一眼就能看出候选人的价值和潜力。

        重要要求：
        1. 直接返回完整的HTML代码，不要任何说明文字
        2. 使用用户的真实信息，但要**大胆地、创造性地**展现其价值
        3. **必须包含Summary部分**：
           - 在联系信息后添加Professional Summary或Summary部分
           - 根据目标职位要求，突出用户最相关的技能和经验
           - 长度控制在3-4句话，简洁有力
           - **关键要求**：必须包含1-2句具体说明用户为什么适合{job_info.get('company', '目标公司')}的{job_info.get('role', '目标职位')}职位
           - 匹配度分析要基于：
             * 用户的相关工作经验与职位要求的对应关系
             * 具体的技能匹配（如管理经验、技术能力、行业背景等）
             * 成就和结果导向的经验
           - 用简洁、自然、说人话的方式表达，避免空洞的套话
           - **示例格式**："具有X年相关经验的专业人士，专长于[具体技能/领域]。在[相关公司/行业]的工作经历使我具备了[目标职位所需的核心能力]，特别是在[具体匹配点]方面的丰富经验，正是[目标公司]寻找的[目标职位]候选人所需要的。"
           - **适应性要求**：根据不同行业和职位类型调整表述风格：
             * 技术职位：强调技术技能、项目经验、解决问题的能力
             * 管理职位：突出领导经验、团队管理、战略思维
             * 销售职位：重点展示业绩成果、客户关系、市场洞察
             * 创意职位：强调创新能力、作品集、创意思维
             * 金融职位：突出分析能力、风险管理、合规经验
           - **Skills部分示例**：
             ```html
             <div class="skills">
                 <span class="skill">项目管理</span>
                 <span class="skill">团队领导</span>
                 <span class="skill">数据分析</span>
             </div>
             ```
        4. **积极优化策略**：
           - 保持所有公司名称、时间段、职位名称完全一致
           - **智能扩充工作描述**：基于用户现有经验和目标职位要求，详细阐述相关工作内容和成就
           - **量化成果展示**：根据职位类型添加合理的量化指标（销售额、团队规模、项目数量、效率提升等）
           - **技能匹配优化**：深度挖掘用户经验中与目标职位相关的技能，并详细描述应用场景
           - **项目经验丰富**：扩展项目描述，包括技术栈、方法论、团队协作、解决方案等
           - **软技能强化**：根据职位要求突出相应的软技能（领导力、沟通能力、创新思维、分析能力等）
           - **行业术语优化**：使用目标行业和职位的专业术语，提升简历的专业度和匹配度
           - **成就导向转化**：将工作职责转化为具体成就、贡献和业务价值
           - **跨行业适应**：根据目标行业调整表述方式，突出可转移的技能和经验
        5. **高亮标记要求**：
           - 对于AI优化、重新表述或扩展的内容，请用 class="ai-optimized" 包装
           - 例如：<span class="ai-optimized">优化后的描述内容</span>
           - 只标记实际被优化的文本部分，不要标记整个段落
           - 原始未修改的内容不需要添加此类
        6. **深度优化原则**：
           - **创造性扩展**：基于用户现有经验，创造性地扩展工作描述，添加合理的细节
           - **专业化表述**：将简单的工作描述转化为专业、详细的成就陈述
           - **技能深挖**：从工作经验中挖掘隐含的技能和能力，详细阐述
           - **影响力放大**：强调工作对公司、团队、项目的积极影响
           - **行业术语丰富**：大量使用目标行业和职位的专业术语
           - **数据驱动**：尽可能添加合理的量化数据（团队规模、项目周期、改进幅度等）
           - **故事化描述**：将工作经验描述得更加生动和具体
        7. **工作经验优化示例（适用于不同职位类型）**：
           - **HR/招聘类**："负责招聘工作" → "<span class='ai-optimized'>领导端到端招聘流程，包括需求分析、候选人筛选、面试协调和入职管理，成功为公司招聘了XX名优秀人才，显著提升了团队效率和人才质量</span>"
           - **管理类**："管理团队" → "<span class='ai-optimized'>管理跨职能团队，通过建立高效的沟通机制和绩效管理体系，提升团队协作效率，成功交付多个关键项目</span>"
           - **技术类**："开发系统" → "<span class='ai-optimized'>设计并开发高可用性系统，采用微服务架构和云原生技术，提升系统性能XX%，支撑日均XX万用户访问</span>"
           - **销售类**："销售产品" → "<span class='ai-optimized'>负责重点客户开发和维护，通过深度需求分析和定制化解决方案，实现销售额增长XX%，客户满意度达到XX%</span>"
           - **财务类**："财务分析" → "<span class='ai-optimized'>建立财务分析模型和风险控制体系，为管理层提供数据驱动的决策支持，优化成本结构，提升盈利能力XX%</span>"
        8. **绝对禁止**：
           - 添加不存在的工作经历或公司
           - 编造完全虚假的技能或经验
           - 修改公司名称、时间或职位标题
           - 完全虚构成就和数据（但可以添加合理的量化描述）
        8. **格式和重复内容要求**：
           - **文字格式**：确保所有单词之间有正确的空格，避免单词连在一起
           - **内容结构**：每个工作经历分为两部分：
             a) 简短的职位概述段落（2-3句话，描述整体职责和影响）
             b) 具体成就的bullet points列表（3-5个要点，展示具体成果）
           - **避免重复**：概述段落和bullet points必须包含不同的信息，不能重复相同内容
           - **示例结构**：
             ```html
             <div class="job">
                 <div class="job-header">
                     <div class="job-title">职位名称</div>
                     <div class="date">时间</div>
                 </div>
                 <div class="company">公司名称</div>
                 <p><span class="ai-optimized">职位概述段落，描述整体职责和影响</span></p>
                 <ul>
                     <li><span class="ai-optimized">具体成就1，包含量化指标</span></li>
                     <li><span class="ai-optimized">具体成就2，展示技能应用</span></li>
                     <li><span class="ai-optimized">具体成就3，体现业务价值</span></li>
                 </ul>
             </div>
             ```
        10. **Skills部分格式要求**：
           - 必须使用正确的HTML结构：<div class="skills"><span class="skill">技能名称</span></div>
           - 每个技能用单独的<span class="skill">标签包装
           - 技能名称要简洁明了，避免过长的描述
           - **智能技能筛选**：根据目标职位要求，优先显示最相关的技能
           - **技能分类优化**：根据职位类型调整技能展示重点：
             * 技术职位：编程语言、框架、工具、平台
             * 管理职位：领导力、战略规划、团队建设、项目管理
             * 销售职位：客户关系、谈判技巧、市场分析、CRM系统
             * 创意职位：设计软件、创意思维、用户体验、品牌策略
             * 分析职位：数据分析、统计软件、建模技能、可视化工具
        11. **Education部分格式要求**：
           - 必须使用专门的HTML结构，类似工作经验的布局
           - 每个教育经历使用以下结构：
             ```html
             <div class="education">
                 <div class="education-header">
                     <div class="degree">学位名称, 专业</div>
                     <div class="education-date">毕业时间</div>
                 </div>
                 <div class="institution">学校名称</div>
             </div>
             ```
           - degree包含学位和专业信息
           - institution显示学校名称
           - education-date显示毕业时间
        12. **Certifications部分格式要求**：
           - 保持简单的文本格式，不要使用Skills的标签样式
           - 每个证书单独一行，使用普通的<p>或<div>标签
           - 格式：证书名称 + 获得时间（如果有的话）
           - 不要添加特殊的CSS类或样式
        13. **工作经验扩展指导**：
           - **内容丰富度**：每个工作经历至少包含3-5个详细的成就描述
           - **动作词使用**：根据职位类型选择合适的动作词：
             * 管理类：领导、管理、协调、规划、决策、监督
             * 技术类：开发、设计、实施、优化、构建、维护
             * 销售类：开拓、维护、谈判、达成、提升、扩展
             * 分析类：分析、评估、建模、预测、优化、改进
           - **工具和方法**：包含具体的工作方法、使用的工具、技术栈、协作平台
           - **问题解决**：强调解决的具体问题、克服的挑战、创新的解决方案
           - **价值创造**：量化展示为公司、团队、项目创造的具体价值和影响
           - **成长体现**：体现持续学习、技能提升、职业发展的轨迹
        14. **通用性和适应性要求**：
           - **行业无关性**：确保优化策略适用于各种行业（科技、金融、制造、教育、医疗、零售等）
           - **职位层级适应**：根据职位层级调整表述（入门级、中级、高级、管理层、高管）
           - **公司规模适应**：考虑目标公司规模（初创公司、中小企业、大型企业、跨国公司）
           - **文化背景适应**：适应不同的企业文化和价值观（创新型、传统型、国际化等）
           - **技能可转移性**：突出跨行业、跨职能的可转移技能
           - **语言本地化**：根据职位地点和公司背景调整语言风格
        15. **样式一致性要求**：
           - 不要在HTML中生成任何高亮切换按钮或相关的JavaScript代码
           - 不要修改现有的CSS样式定义
           - 确保每次生成的页面布局和样式保持一致
           - 只在内容中添加ai-optimized类，不要修改其他样式
           - 高亮功能将由系统自动添加，请勿在生成的HTML中包含
        16. 专业美观的格式

        请直接返回HTML代码，格式如下：

        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{name} - 简历</title>
            <style>
                * {{ box-sizing: border-box; }}
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    line-height: 1.6;
                }}
                .resume {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .name {{
                    font-size: 2.2em;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                    word-break: break-word;
                }}
                .contact {{
                    color: #7f8c8d;
                    font-size: 1em;
                    word-break: break-all;
                }}
                .summary {{
                    font-size: 1.1em;
                    line-height: 1.7;
                    color: #34495e;
                    text-align: justify;
                    margin-bottom: 5px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .section {{
                    margin-bottom: 25px;
                    clear: both;
                }}
                .section-title {{
                    font-size: 1.4em;
                    font-weight: bold;
                    color: #2c3e50;
                    border-bottom: 1px solid #bdc3c7;
                    padding-bottom: 5px;
                    margin-bottom: 15px;
                }}
                .job {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .job-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .job-title {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .company {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                .description {{
                    margin-top: 8px;
                    text-align: justify;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .skills {{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                }}
                .skill {{
                    background: #3498db;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 0.85em;
                    white-space: nowrap;
                }}
                /* Education部分样式 */
                .education {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .education-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .degree {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .institution {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .education-date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                /* 高亮优化内容的样式 - 仅在预览中显示 */
                .ai-optimized {{
                    background-color: rgba(25, 118, 210, 0.12);
                    border-left: 3px solid #1976d2;
                    padding: 2px 6px 2px 8px;
                    margin: 2px 0;
                    border-radius: 4px;
                    position: relative;
                    display: inline;
                    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.1);
                }}
                .ai-optimized::before {{
                    content: "✨";
                    position: absolute;
                    left: -18px;
                    top: 0;
                    font-size: 12px;
                    z-index: 10;
                    opacity: 0.8;
                    transition: opacity 0.2s ease;
                    line-height: 1;
                }}
                .ai-optimized:hover::before {{
                    opacity: 1;
                }}
                /* 隐藏高亮时的样式 */
                .ai-optimized-hidden {{
                    background-color: transparent !important;
                    border-left: none !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    box-shadow: none !important;
                }}
                .ai-optimized-hidden::before {{
                    display: none !important;
                }}
                /* 高亮切换按钮样式 */
                .highlight-toggle {{
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    padding: 4px;
                }}
                .highlight-toggle button {{
                    background: linear-gradient(135deg, #1976d2, #1565c0);
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 0.8em;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
                }}
                .highlight-toggle button:hover {{
                    background: linear-gradient(135deg, #1565c0, #0d47a1);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
                }}
                .highlight-toggle button:active {{
                    transform: translateY(0);
                }}
                /* PDF生成时隐藏高亮样式 */
                @media print {{
                    .ai-optimized {{
                        background-color: transparent !important;
                        border-left: none !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        box-shadow: none !important;
                    }}
                    .ai-optimized::before {{
                        display: none !important;
                    }}
                    .highlight-toggle {{
                        display: none !important;
                    }}
                }}
                @media (max-width: 600px) {{
                    .resume {{ padding: 20px; }}
                    .name {{ font-size: 1.8em; }}
                    .contact {{ font-size: 0.9em; }}
                    .job-header {{ flex-direction: column; }}
                    .date {{ margin-left: 0; margin-top: 5px; }}
                    .education-header {{ flex-direction: column; }}
                    .education-date {{ margin-left: 0; margin-top: 5px; }}
                }}
            </style>
        </head>
        <body>
            <div class="resume">
                <div class="header">
                    <div class="name">{name}</div>
                    <div class="contact">{phone} | {email} | {address}</div>
                </div>

                <div class="section">
                    <div class="section-title">Professional Summary</div>
                    <div class="summary">
                        <!-- AI将在这里生成针对目标职位的专业总结 -->
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">Work Experience</div>
                    <!-- AI将在这里生成优化后的工作经历 -->
                </div>

                <div class="section">
                    <div class="section-title">Education</div>
                    <!-- AI将在这里生成教育背景 -->
                </div>

                <div class="section">
                    <div class="section-title">Skills</div>
                    <div class="skills">
                        <!-- AI将在这里生成技能标签，格式如：<span class="skill">技能名称</span> -->
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        # 使用LLM生成优化简历
        if hasattr(llm_parser.llm, 'invoke'):
            response = llm_parser.llm.invoke(optimization_prompt)
        elif hasattr(llm_parser.llm, 'predict'):
            response = llm_parser.llm.predict(optimization_prompt)
        elif hasattr(llm_parser.llm, 'llm') and hasattr(llm_parser.llm.llm, 'invoke'):
            response = llm_parser.llm.llm.invoke(optimization_prompt)
        else:
            raise Exception("无法调用LLM模型")

        # 提取响应内容
        if hasattr(response, 'content'):
            optimized_content = response.content
        else:
            optimized_content = str(response)

        # 清理HTML内容
        cleaned_content = clean_html_content(optimized_content)

        return cleaned_content

    except Exception as e:
        logger.error(f"简历优化失败: {str(e)}")
        raise e

def clean_html_content(html_content):
    """
    清理LLM生成的HTML内容，去除多余的说明文字
    """
    import re

    # 去除开头的说明文字
    patterns_to_remove = [
        r'^.*?(?=<!DOCTYPE|<html)',  # 去除HTML开始前的所有文字
        r'```html\s*',  # 去除```html标记
        r'```\s*$',     # 去除结尾的```标记
        r'\*\*.*?\*\*',  # 去除**包围的文字
        r'CSS.*?(?=</style>)',  # 去除CSS说明文字
        r'```css.*?```',  # 去除CSS代码块
    ]

    cleaned = html_content
    for pattern in patterns_to_remove:
        cleaned = re.sub(pattern, '', cleaned, flags=re.DOTALL | re.MULTILINE)

    # 确保HTML结构完整
    if not cleaned.strip().startswith('<!DOCTYPE') and not cleaned.strip().startswith('<html'):
        # 如果没有完整的HTML结构，提取HTML部分
        html_match = re.search(r'<!DOCTYPE.*?</html>', cleaned, re.DOTALL)
        if html_match:
            cleaned = html_match.group(0)

    return cleaned.strip()

def validate_api_key():
    """验证API密钥的有效性和类型"""
    if not global_config.API_KEY:
        print("错误: API密钥未配置")
        return False, "API密钥未配置，请检查secrets.yaml文件"
    
    # 检查API密钥格式（Gemini API密钥通常以'AIza'开头）
    if not global_config.API_KEY.startswith('AIza'):
        print("错误: API密钥格式不正确，请确保使用有效的Gemini API密钥")
        return False, "API密钥格式不正确，请确保使用有效的Gemini API密钥"
    
    return True, ""

class ResumeGenerateRequest(BaseModel):
    job_url: str
    user_resume: Optional[dict] = None

@app.post("/api/resume/upload")
async def upload_resume(resume: UploadFile = File(...)):
    """
    上传并解析用户简历
    """
    try:
        # 验证文件类型
        if resume.content_type != "application/pdf":
            return {"status": "error", "message": "只支持PDF格式的简历文件"}

        # 读取文件内容
        content = await resume.read()

        # 解析PDF内容
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(content))
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"

            if not text_content.strip():
                return {"status": "error", "message": "无法从PDF中提取文本内容"}

            # 使用LLM解析简历内容
            from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
            llm_parser = LLMParser(api_key=api_key)
            resume_data = llm_parser.parse_resume_content(text_content)

            return {
                "status": "success",
                "message": "简历上传并解析成功",
                "resume_data": resume_data
            }

        except Exception as parse_error:
            return {"status": "error", "message": f"简历解析失败: {str(parse_error)}"}

    except Exception as e:
        return {"status": "error", "message": f"文件上传失败: {str(e)}"}

@app.post("/api/resume/generate")
async def generate_resume(request: ResumeGenerateRequest):
    job_url = request.job_url
    user_resume = request.user_resume

    if not job_url or not job_url.startswith('http'):
        return {"status": "error", "message": "无效的职位URL，请提供有效的网址"}

    try:
        # 检查组件初始化状态
        if not resume_facade.resume_generator or not resume_facade.style_manager:
            print("错误: 系统组件未正确初始化")
            return {"status": "error", "message": "系统组件未正确初始化，请检查系统配置"}

        # 验证API密钥
        is_valid, error_message = validate_api_key()
        if not is_valid:
            print(f"API密钥验证失败: {error_message}")
            return {"status": "warning", "message": f"API密钥问题: {error_message}，将使用降级模式解析"}

        # 如果用户上传了简历，设置用户简历数据
        if user_resume:
            print("检测到用户上传的简历，将生成针对性优化简历")
            resume_facade.set_user_resume(user_resume)

        # 调用原有逻辑
        print(f"开始处理职位URL: {job_url}")

        try:
            resume_facade.link_to_job(job_url)

            # 检查是否成功解析到职位信息
            if hasattr(resume_facade, 'job') and resume_facade.job:
                job_info = {
                    "role": resume_facade.job.role or "未知职位",
                    "company": resume_facade.job.company or "未知公司",
                    "location": resume_facade.job.location or "未知地点",
                    "description": resume_facade.job.description or "职位描述信息不完整",
                    "url": resume_facade.job.link or job_url
                }

                # 检查是否使用了降级模式
                fallback_used = False
                if hasattr(resume_facade, 'llm_job_parser') and hasattr(resume_facade.llm_job_parser, 'fallback_mode'):
                    fallback_used = resume_facade.llm_job_parser.fallback_mode

                success_message = "职位信息已成功解析"
                if fallback_used:
                    success_message += "（使用降级模式）"

                print(f"成功解析职位信息: {job_info}")

                # 如果有用户简历数据，生成优化的简历内容
                optimized_resume = None
                if user_resume:
                    try:
                        optimized_resume = generate_optimized_resume_content(user_resume, job_info)
                        # 添加高亮标记到优化后的简历
                        optimized_resume = add_highlight_to_optimized_content(user_resume, optimized_resume)
                        print("成功生成优化简历内容并添加高亮标记")
                    except Exception as e:
                        print(f"简历优化失败: {str(e)}")

                return {
                    "status": "success",
                    "message": success_message,
                    "job_info": job_info,
                    "fallback_mode": fallback_used,
                    "optimized_resume": optimized_resume
                }
            else:
                return {"status": "error", "message": "无法解析职位信息，请检查URL是否有效"}

        except Exception as parse_error:
            print(f"职位解析错误: {str(parse_error)}")
            # 尝试提供基本的错误恢复
            return {
                "status": "error",
                "message": f"职位解析失败: {str(parse_error)}。可能的原因：网络连接问题、页面结构变化或API服务不可用。"
            }

    except AttributeError as e:
        error_msg = f"组件属性错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}
    except ConnectionError as e:
        error_msg = f"网络连接错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": f"网络连接失败: {error_msg}。请检查网络连接或稍后重试。"}
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": f"系统错误: {error_msg}。请稍后重试或联系技术支持。"}

@app.post("/api/resume/generate-pdf")
async def generate_resume_pdf(request: dict):
    """
    生成PDF简历
    Args:
        request: 包含optimized_resume_html的请求数据
    Returns:
        PDF文件的base64编码
    """
    try:
        optimized_resume_html = request.get('optimized_resume_html')

        if optimized_resume_html:
            # 使用优化后的HTML内容生成PDF
            return await generate_pdf_from_html(optimized_resume_html)
        else:
            return {"status": "error", "message": "缺少简历HTML内容"}

    except Exception as e:
        error_msg = f"PDF生成失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}

def add_highlight_to_optimized_content(original_resume, optimized_html):
    """
    为优化后的简历内容添加高亮标记
    """
    try:
        if not original_resume or not optimized_html:
            return optimized_html

        # 添加高亮切换按钮和脚本
        toggle_button = '''
        <div class="highlight-toggle">
            <button onclick="toggleHighlight()" id="highlightToggleBtn">
                💡 隐藏高亮
            </button>
        </div>
        <script>
        let highlightVisible = true;

        function toggleHighlight() {
            const elements = document.querySelectorAll('.ai-optimized');
            const button = document.getElementById('highlightToggleBtn');

            if (highlightVisible) {
                // 隐藏高亮 - 添加hidden类
                elements.forEach(el => {
                    el.classList.add('ai-optimized-hidden');
                });
            } else {
                // 显示高亮 - 移除hidden类
                elements.forEach(el => {
                    el.classList.remove('ai-optimized-hidden');
                });
            }

            highlightVisible = !highlightVisible;
            button.innerHTML = highlightVisible ? '💡 隐藏高亮' : '✨ 显示高亮';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.ai-optimized');
            if (elements.length > 0) {
                console.log('找到 ' + elements.length + ' 个AI优化标记');
            }
        });
        </script>
        '''

        # 在body结束标签前插入切换按钮
        highlighted_html = optimized_html
        if '</body>' in highlighted_html:
            highlighted_html = highlighted_html.replace('</body>', toggle_button + '</body>')

        return highlighted_html

    except Exception as e:
        print(f"添加高亮标记时出错: {str(e)}")
        return optimized_html

def remove_highlight_for_pdf(html_content):
    """
    移除PDF生成时的高亮样式和切换按钮
    """
    try:
        import re

        # 移除切换按钮div
        html_content = re.sub(r'<div class="highlight-toggle".*?</div>', '', html_content, flags=re.DOTALL)

        # 移除脚本
        html_content = re.sub(r'<script>.*?</script>', '', html_content, flags=re.DOTALL)

        # 移除ai-optimized类，但保留内容
        html_content = re.sub(r'<([^>]+)\s+class="[^"]*ai-optimized[^"]*"([^>]*)>', r'<\1\2>', html_content)
        html_content = re.sub(r'class="[^"]*ai-optimized[^"]*"\s*', '', html_content)

        return html_content

    except Exception as e:
        print(f"移除高亮样式时出错: {str(e)}")
        return html_content

async def generate_pdf_from_html(html_content):
    """
    从HTML内容生成PDF
    """
    try:
        import tempfile
        import os
        import base64
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        import time

        # 清理HTML内容，提取实际的HTML代码
        if '```html' in html_content:
            # 提取HTML代码块
            start = html_content.find('```html') + 7
            end = html_content.find('```', start)
            if end != -1:
                html_content = html_content[start:end].strip()

        # 移除高亮样式，确保PDF中不显示高亮
        html_content = remove_highlight_for_pdf(html_content)

        # 创建临时HTML文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            temp_html_path = f.name

        try:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--print-to-pdf')

            # 初始化WebDriver
            from src.utils.chrome_utils import init_browser
            driver = init_browser()

            # 加载HTML文件
            file_url = f"file:///{temp_html_path.replace(os.sep, '/')}"
            driver.get(file_url)
            time.sleep(2)  # 等待页面加载

            # 生成PDF
            pdf_data = driver.execute_cdp_cmd('Page.printToPDF', {
                'format': 'A4',
                'printBackground': True,
                'marginTop': 0.4,
                'marginBottom': 0.4,
                'marginLeft': 0.4,
                'marginRight': 0.4
            })

            driver.quit()

            # 获取PDF的base64数据
            pdf_base64 = pdf_data['data']

            # 生成文件名
            import uuid
            filename = str(uuid.uuid4())[:8]

            return {
                "status": "success",
                "message": "PDF简历生成成功",
                "pdf_data": pdf_base64,
                "filename": f"optimized_resume_{filename}.pdf"
            }

        finally:
            # 清理临时文件
            if os.path.exists(temp_html_path):
                os.unlink(temp_html_path)

    except Exception as e:
        error_msg = f"HTML转PDF失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}



if __name__ == "__main__":
    import uvicorn
    print("启动 FastAPI 服务器...")
    print("LinkedIn API 可在 http://localhost:8002/api/linkedin/ 访问")
    print("简历生成 API 可在 http://localhost:8002/api/resume/ 访问")
    print("健康检查 API 可在 http://localhost:8002/api/health 访问")
    uvicorn.run(app, host="0.0.0.0", port=8002)