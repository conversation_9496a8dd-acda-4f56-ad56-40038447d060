#!/usr/bin/env python3
"""
测试降级模式的职位解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.libs.resume_and_cover_builder.llm.llm_job_parser import <PERSON><PERSON>ars<PERSON>

def test_fallback_mode():
    """测试降级模式"""
    print("测试降级模式职位解析...")

    # 创建解析器并强制使用降级模式
    # 提供一个虚拟的API密钥以通过初始化
    parser = LLMParser(api_key="dummy_key")
    parser.fallback_mode = True
    
    # 模拟LinkedIn职位页面HTML内容
    test_html = """
    <html>
    <head>
        <title>Software Engineer - MathWorks</title>
    </head>
    <body>
        <div class="job-details">
            <h1>Software Engineer</h1>
            <div class="company-info">
                <span>Company: MathWorks</span>
                <span>Location: Boston, MA</span>
            </div>
            <div class="job-description">
                <p>Job Description: We are looking for a talented Software Engineer to join our team at MathWorks.
                The role involves developing innovative software solutions for mathematical computing.</p>
                <p>Responsibilities include designing, implementing, and testing software applications.</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 设置HTML内容
    parser.set_body_html(test_html)
    
    # 测试各种信息提取
    print("\n=== 测试结果 ===")
    
    try:
        company = parser.extract_company_name()
        print(f"公司名称: {company}")
    except Exception as e:
        print(f"公司名称提取失败: {e}")
    
    try:
        role = parser.extract_role()
        print(f"职位名称: {role}")
    except Exception as e:
        print(f"职位名称提取失败: {e}")
    
    try:
        location = parser.extract_location()
        print(f"工作地点: {location}")
    except Exception as e:
        print(f"工作地点提取失败: {e}")
    
    try:
        description = parser.extract_job_description()
        print(f"职位描述: {description[:100]}...")
    except Exception as e:
        print(f"职位描述提取失败: {e}")

if __name__ == "__main__":
    test_fallback_mode()
