#!/usr/bin/env python3
"""
测试修复后的LinkedIn职位抓取功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.linkedin_automation_playwright import LinkedInAutomationPlaywright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_html_parsing_with_fixed_selectors():
    """测试修复后的HTML解析功能"""
    logger.info("测试修复后的HTML解析功能...")
    
    html_file = "log/linkedin_jobs_live.html"
    if not os.path.exists(html_file):
        logger.error(f"HTML文件 {html_file} 不存在")
        logger.info("请先运行实时抓取测试生成HTML文件")
        return False
    
    try:
        # 创建LinkedIn自动化实例
        test_config = {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': True,
                'timeout': 30000
            }
        }
        
        linkedin = LinkedInAutomationPlaywright(test_config)
        
        # 解析HTML文件
        jobs = linkedin.parse_jobs_from_html(html_file)
        
        logger.info(f"HTML解析完成，找到 {len(jobs)} 个职位")
        
        # 显示所有职位的详细信息
        for i, job in enumerate(jobs):
            print(f"\n=== 职位 {i+1} ===")
            print(f"标题: {job.get('title', 'N/A')}")
            print(f"公司: {job.get('company', 'N/A')}")
            print(f"地点: {job.get('location', 'N/A')}")
            print(f"Easy Apply: {job.get('is_easy_apply', False)}")
            print(f"URL: {job.get('url', 'N/A')}")
            print(f"Job ID: {job.get('job_id', 'N/A')}")
        
        # 验证解析质量
        valid_jobs = 0
        easy_apply_jobs = 0
        
        for job in jobs:
            if (job.get('title') != '未知职位' and 
                job.get('company') != '未知公司' and 
                job.get('location') != '未知地点' and
                job.get('url') and '/jobs/view/' in job.get('url', '')):
                valid_jobs += 1
            
            if job.get('is_easy_apply'):
                easy_apply_jobs += 1
        
        print(f"\n=== 解析质量统计 ===")
        print(f"总职位数: {len(jobs)}")
        print(f"有效职位数: {valid_jobs}")
        print(f"Easy Apply职位数: {easy_apply_jobs}")
        print(f"有效率: {valid_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
        print(f"Easy Apply比例: {easy_apply_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
        
        # 判断测试是否成功
        if len(jobs) > 0 and valid_jobs > 0:
            logger.info("✅ 修复后的HTML解析测试成功！")
            return True
        else:
            logger.warning("⚠️ 修复后的HTML解析仍未找到有效职位")
            return False
            
    except Exception as e:
        logger.error(f"HTML解析测试失败: {str(e)}")
        return False

def test_live_scraping_with_fixed_selectors():
    """测试修复后的实时抓取功能"""
    logger.info("测试修复后的实时抓取功能...")
    
    try:
        # 创建LinkedIn自动化实例
        test_config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20121108@@',
                'search_keywords': ['python developer'],
                'location': 'United States',
                'max_applications_per_day': 5
            },
            'playwright': {
                'headless': False,
                'timeout': 60000,
                'window_size': [1200, 800]
            }
        }
        
        with LinkedInAutomationPlaywright(test_config) as linkedin:
            logger.info("正在登录LinkedIn...")
            
            # 登录
            login_result = linkedin.login()
            if not login_result:
                logger.warning("登录失败，尝试不登录直接搜索...")
            
            logger.info("开始搜索职位...")
            
            # 搜索职位
            jobs = linkedin.search_jobs(
                keywords='python developer',
                location='United States',
                easy_apply_only=False  # 不限制Easy Apply，获取更多职位
            )
            
            logger.info(f"搜索完成，找到 {len(jobs)} 个职位")
            
            # 显示前5个职位的详细信息
            for i, job in enumerate(jobs[:5]):
                print(f"\n=== 职位 {i+1} ===")
                print(f"标题: {job.get('title', 'N/A')}")
                print(f"公司: {job.get('company', 'N/A')}")
                print(f"地点: {job.get('location', 'N/A')}")
                print(f"Easy Apply: {job.get('is_easy_apply', False)}")
                print(f"URL: {job.get('url', 'N/A')}")
                print(f"Job ID: {job.get('job_id', 'N/A')}")
            
            # 验证抓取质量
            valid_jobs = 0
            easy_apply_jobs = 0
            
            for job in jobs:
                if (job.get('title') != '未知职位' and 
                    job.get('company') != '未知公司' and 
                    job.get('location') != '未知地点'):
                    valid_jobs += 1
                
                if job.get('is_easy_apply'):
                    easy_apply_jobs += 1
            
            print(f"\n=== 抓取质量统计 ===")
            print(f"总职位数: {len(jobs)}")
            print(f"有效职位数: {valid_jobs}")
            print(f"Easy Apply职位数: {easy_apply_jobs}")
            print(f"有效率: {valid_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
            print(f"Easy Apply比例: {easy_apply_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
            
            # 判断测试是否成功
            if len(jobs) > 0 and valid_jobs > 0:
                logger.info("✅ 修复后的实时抓取测试成功！")
                return True
            else:
                logger.error("❌ 修复后的实时抓取测试失败：未能抓取到有效职位信息")
                return False
                
    except Exception as e:
        logger.error(f"实时抓取测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 LinkedIn职位抓取功能修复验证")
    print("=" * 50)
    
    # 测试1: HTML解析功能
    print("\n📄 测试1: 修复后的HTML解析功能")
    html_success = test_html_parsing_with_fixed_selectors()
    
    # 测试2: 实时抓取功能
    print("\n🌐 测试2: 修复后的实时抓取功能")
    live_success = test_live_scraping_with_fixed_selectors()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 修复验证结果:")
    print(f"HTML解析: {'✅ 成功' if html_success else '❌ 失败'}")
    print(f"实时抓取: {'✅ 成功' if live_success else '❌ 失败'}")
    
    if html_success and live_success:
        print("\n🎉 LinkedIn职位抓取功能修复成功！")
        print("💡 功能特点：")
        print("  - 使用LinkedIn实际的CSS选择器")
        print("  - 支持职位标题、公司名称、地点信息抓取")
        print("  - 支持Easy Apply状态检测")
        print("  - 过滤星号遮挡的内容")
        print("  - 提取Job ID用于去重")
        print("\n🚀 可以开始使用批量申请功能了！")
    elif html_success:
        print("\n✅ HTML解析功能修复成功！")
        print("⚠️ 实时抓取可能需要进一步调试")
    else:
        print("\n⚠️ 功能修复需要进一步完善")
        print("💡 建议：")
        print("  - 检查LinkedIn页面结构是否有新变化")
        print("  - 验证网络连接和登录状态")
        print("  - 更新CSS选择器")

if __name__ == "__main__":
    main()
