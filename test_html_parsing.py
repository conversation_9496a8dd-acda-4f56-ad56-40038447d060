#!/usr/bin/env python3
"""
LinkedIn HTML解析测试脚本
专门测试HTML解析功能，不需要登录
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.linkedin_automation_playwright import LinkedInAutomationPlaywright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_html_parsing():
    """测试HTML解析功能"""
    logger.info("开始测试LinkedIn HTML解析功能...")
    
    html_file = "log/linkedin_jobs_full_page.html"
    if not os.path.exists(html_file):
        logger.error(f"HTML文件 {html_file} 不存在")
        logger.info("请先运行LinkedIn搜索功能生成HTML快照文件")
        return False
    
    try:
        # 创建LinkedIn自动化实例（仅用于解析）
        test_config = {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': True,
                'timeout': 30000
            }
        }
        
        linkedin = LinkedInAutomationPlaywright(test_config)
        
        # 解析HTML文件
        jobs = linkedin.parse_jobs_from_html(html_file)
        
        logger.info(f"HTML解析完成，找到 {len(jobs)} 个职位")
        
        # 显示所有职位的详细信息
        for i, job in enumerate(jobs):
            print(f"\n=== 职位 {i+1} ===")
            print(f"标题: {job.get('title', 'N/A')}")
            print(f"公司: {job.get('company', 'N/A')}")
            print(f"地点: {job.get('location', 'N/A')}")
            print(f"Easy Apply: {job.get('is_easy_apply', False)}")
            print(f"URL: {job.get('url', 'N/A')}")
            print(f"Job ID: {job.get('job_id', 'N/A')}")
        
        # 验证解析质量
        valid_jobs = 0
        easy_apply_jobs = 0
        
        for job in jobs:
            if (job.get('title') != '未知职位' and 
                job.get('company') != '未知公司' and 
                job.get('location') != '未知地点' and
                job.get('url') and '/jobs/view/' in job.get('url', '')):
                valid_jobs += 1
            
            if job.get('is_easy_apply'):
                easy_apply_jobs += 1
        
        print(f"\n=== 解析质量统计 ===")
        print(f"总职位数: {len(jobs)}")
        print(f"有效职位数: {valid_jobs}")
        print(f"Easy Apply职位数: {easy_apply_jobs}")
        print(f"有效率: {valid_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
        print(f"Easy Apply比例: {easy_apply_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
        
        # 判断测试是否成功
        if len(jobs) > 0 and valid_jobs > 0:
            logger.info("✅ HTML解析测试成功！")
            return True
        else:
            logger.warning("⚠️ HTML解析未找到有效职位，可能需要更新选择器")
            return False
            
    except Exception as e:
        logger.error(f"HTML解析测试失败: {str(e)}")
        return False

def analyze_html_structure():
    """分析HTML文件结构，帮助调试选择器"""
    logger.info("分析HTML文件结构...")
    
    html_file = "log/linkedin_jobs_full_page.html"
    if not os.path.exists(html_file):
        logger.error(f"HTML文件 {html_file} 不存在")
        return
    
    try:
        from bs4 import BeautifulSoup
        
        with open(html_file, "r", encoding="utf-8") as f:
            html = f.read()
        
        soup = BeautifulSoup(html, "html.parser")
        
        print("\n=== HTML结构分析 ===")
        
        # 分析li元素
        all_li = soup.select("li")
        print(f"总li元素数量: {len(all_li)}")
        
        # 分析data-job-id属性的li
        job_id_li = soup.select("li[data-job-id]")
        print(f"带data-job-id的li元素: {len(job_id_li)}")
        
        # 分析包含职位链接的li
        job_link_li = soup.select("li:has(.job-card-job-posting-card-wrapper__card-link)")
        print(f"包含职位链接的li元素: {len(job_link_li)}")
        
        # 分析artdeco-entity-lockup结构
        entity_lockup = soup.select(".artdeco-entity-lockup")
        print(f"artdeco-entity-lockup元素: {len(entity_lockup)}")
        
        # 显示前几个li元素的类名和内容片段
        print(f"\n=== 前10个li元素分析 ===")
        for i, li in enumerate(all_li[:10]):
            classes = li.get('class', [])
            data_job_id = li.get('data-job-id', '')
            text_content = li.get_text(strip=True)[:100]  # 前100个字符
            
            print(f"Li {i+1}:")
            print(f"  类名: {classes}")
            print(f"  data-job-id: {data_job_id}")
            print(f"  内容片段: {text_content}")
            print()
            
    except Exception as e:
        logger.error(f"分析HTML结构失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 LinkedIn HTML解析功能测试")
    print("=" * 50)
    
    # 分析HTML结构
    print("\n📊 步骤1: 分析HTML结构")
    analyze_html_structure()
    
    # 测试HTML解析
    print("\n📄 步骤2: 测试HTML解析")
    parsing_success = test_html_parsing()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"HTML解析: {'✅ 成功' if parsing_success else '❌ 失败'}")
    
    if parsing_success:
        print("\n🎉 HTML解析功能正常！")
        print("💡 建议：")
        print("  - 可以继续测试实时抓取功能")
        print("  - 检查Easy Apply检测是否准确")
    else:
        print("\n⚠️ HTML解析需要进一步调试")
        print("💡 建议：")
        print("  - 检查HTML文件是否包含职位信息")
        print("  - 更新CSS选择器以匹配新的LinkedIn结构")
        print("  - 确认HTML快照是职位搜索页面而不是其他页面")

if __name__ == "__main__":
    main()
