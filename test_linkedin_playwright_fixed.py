#!/usr/bin/env python3
"""
LinkedIn Playwright修复后的完整测试
解决了网络连接、异步事件循环和配置问题
"""

import asyncio
import sys
import traceback
import logging
import time
import yaml
import tempfile
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_linkedin_playwright_sync_fixed():
    """测试LinkedIn Playwright同步版本（修复版）"""
    logger.info("=== 测试LinkedIn Playwright同步版本（修复版） ===")
    try:
        from src.linkedin_automation_playwright import LinkedInAutomationPlaywright
        
        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        logger.info("1. 创建LinkedIn自动化实例...")
        linkedin = LinkedInAutomationPlaywright(config)
        logger.info("LinkedIn自动化实例创建成功")
        
        logger.info("2. 设置浏览器（禁用代理）...")
        # 清空代理池，确保不使用代理
        linkedin._PROXY_POOL = []
        page = linkedin.setup_driver(headless=False, proxy=None)
        logger.info("浏览器设置成功")
        
        logger.info("3. 访问LinkedIn主页...")
        page.goto("https://www.linkedin.com", timeout=30000)
        time.sleep(2)
        title = page.title()
        logger.info(f"页面标题: {title}")
        
        logger.info("4. 尝试登录...")
        login_result = linkedin.login()
        logger.info(f"登录结果: {login_result}")
        
        if login_result.get('success'):
            logger.info("✅ 登录成功")
            
            # 测试职位搜索
            logger.info("5. 测试职位搜索...")
            try:
                jobs = linkedin.search_jobs(
                    keywords='python developer',
                    location='United States',
                    easy_apply_only=False
                )
                logger.info(f"找到 {len(jobs)} 个职位")
                
                if len(jobs) > 0:
                    logger.info("✅ 职位搜索成功")
                    # 显示前3个职位
                    for i, job in enumerate(jobs[:3]):
                        logger.info(f"职位 {i+1}: {job.get('title')} - {job.get('company')}")
                else:
                    logger.warning("⚠️ 未找到职位")
            except Exception as e:
                logger.error(f"职位搜索失败: {str(e)}")
                
        elif login_result.get('requires_action'):
            logger.info("⚠️ 需要用户操作（如2FA验证）")
            logger.info("请在浏览器中完成验证，然后按Enter继续...")
            input()
            
            # 验证登录状态
            verify_result = linkedin.verify_login_status()
            if verify_result.get('success'):
                logger.info("✅ 验证后登录成功")
            else:
                logger.warning(f"❌ 验证失败: {verify_result.get('status')}")
        else:
            logger.warning(f"❌ 登录失败: {login_result.get('status')}")
        
        logger.info("6. 清理资源...")
        linkedin.close()
        logger.info("✅ LinkedIn Playwright同步版本测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ LinkedIn Playwright同步版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

async def test_linkedin_playwright_async_fixed():
    """测试LinkedIn Playwright异步版本（修复版）"""
    logger.info("=== 测试LinkedIn Playwright异步版本（修复版） ===")
    try:
        from src.linkedin_automation_playwright_async import LinkedInAutomationPlaywrightAsync
        
        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        logger.info("1. 创建LinkedIn异步自动化实例...")
        linkedin = LinkedInAutomationPlaywrightAsync(config)
        logger.info("LinkedIn异步自动化实例创建成功")
        
        logger.info("2. 设置异步浏览器...")
        page = await linkedin.setup_driver(headless=False)
        logger.info("异步浏览器设置成功")
        
        logger.info("3. 访问LinkedIn主页...")
        await page.goto("https://www.linkedin.com", timeout=30000)
        await asyncio.sleep(2)
        title = await page.title()
        logger.info(f"页面标题: {title}")
        
        logger.info("4. 尝试异步登录...")
        login_result = await linkedin.login()
        logger.info(f"登录结果: {login_result}")
        
        if login_result.get('success'):
            logger.info("✅ 异步登录成功")
        elif login_result.get('requires_action'):
            logger.info("⚠️ 需要用户操作（如2FA验证）")
            logger.info("等待30秒供用户完成验证...")
            await asyncio.sleep(30)
        else:
            logger.warning(f"❌ 异步登录失败: {login_result.get('status')}")
        
        logger.info("5. 清理异步资源...")
        await linkedin.close()
        logger.info("✅ LinkedIn Playwright异步版本测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ LinkedIn Playwright异步版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_linkedin_selenium_fixed():
    """测试LinkedIn Selenium版本（修复版）"""
    logger.info("=== 测试LinkedIn Selenium版本（修复版） ===")
    try:
        from src.linkedin_automation import LinkedInAutomation
        
        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'selenium': {
                'headless': False,
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'window_size': [1920, 1080]
            }
        }
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config, f, default_flow_style=False)
            temp_config_path = f.name
        
        try:
            logger.info("1. 创建LinkedIn Selenium自动化实例...")
            linkedin = LinkedInAutomation(temp_config_path)
            logger.info("LinkedIn Selenium自动化实例创建成功")
            
            logger.info("2. 设置Selenium浏览器...")
            driver = linkedin.setup_driver(headless=False)
            logger.info("Selenium浏览器设置成功")
            
            logger.info("3. 访问LinkedIn主页...")
            driver.get("https://www.linkedin.com")
            time.sleep(2)
            title = driver.title
            logger.info(f"页面标题: {title}")
            
            logger.info("4. 尝试Selenium登录...")
            login_result = linkedin.login()
            logger.info(f"登录结果: {login_result}")
            
            if login_result.get('success'):
                logger.info("✅ Selenium登录成功")
            elif login_result.get('requires_action'):
                logger.info("⚠️ 需要用户操作（如2FA验证）")
                logger.info("请在浏览器中完成验证，然后按Enter继续...")
                input()
            else:
                logger.warning(f"❌ Selenium登录失败: {login_result.get('status')}")
            
            logger.info("5. 清理Selenium资源...")
            linkedin.close()
            logger.info("✅ LinkedIn Selenium版本测试完成")
            return True
            
        finally:
            # 清理临时文件
            os.unlink(temp_config_path)
        
    except Exception as e:
        logger.error(f"❌ LinkedIn Selenium版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def run_async_test_properly():
    """正确运行异步测试"""
    try:
        # 检查是否已有事件循环
        try:
            loop = asyncio.get_running_loop()
            logger.warning("检测到运行中的事件循环，无法在同步环境中正确测试异步代码")
            logger.info("建议在独立的异步环境中测试异步版本")
            return False
        except RuntimeError:
            # 没有运行中的循环，可以创建新的
            logger.info("创建新的事件循环进行异步测试")
            return asyncio.run(test_linkedin_playwright_async_fixed())
    except Exception as e:
        logger.error(f"运行异步测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始LinkedIn Playwright修复后的完整测试...")
    
    # 测试顺序：Selenium -> Playwright同步 -> Playwright异步
    
    # 1. 测试Selenium版本（应该工作）
    logger.info("\n" + "="*60)
    selenium_success = test_linkedin_selenium_fixed()
    
    # 2. 测试Playwright同步版本
    logger.info("\n" + "="*60)
    sync_success = test_linkedin_playwright_sync_fixed()
    
    # 3. 测试Playwright异步版本
    logger.info("\n" + "="*60)
    async_success = run_async_test_properly()
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info("=== 最终测试总结 ===")
    logger.info(f"Selenium版本: {'✅ 成功' if selenium_success else '❌ 失败'}")
    logger.info(f"Playwright同步版本: {'✅ 成功' if sync_success else '❌ 失败'}")
    logger.info(f"Playwright异步版本: {'✅ 成功' if async_success else '❌ 失败'}")
    
    # 分析结果
    if selenium_success and sync_success and async_success:
        logger.info("🎉 所有版本都工作正常！问题已完全解决")
    elif selenium_success and sync_success:
        logger.info("✅ Selenium和Playwright同步版本工作正常")
        logger.warning("⚠️ 异步版本可能需要在独立环境中测试")
    elif selenium_success:
        logger.warning("⚠️ 只有Selenium版本工作，Playwright版本仍有问题")
        logger.info("建议检查Playwright的配置和依赖")
    else:
        logger.error("❌ 所有版本都有问题，需要进一步调试")
    
    logger.info("\n修复要点总结:")
    logger.info("1. ✅ 禁用代理配置解决网络连接问题")
    logger.info("2. ✅ 正确处理配置文件/字典传递")
    logger.info("3. ✅ 修复异步事件循环冲突")
    logger.info("4. ✅ 添加更好的错误处理和重试机制")

if __name__ == "__main__":
    main()
