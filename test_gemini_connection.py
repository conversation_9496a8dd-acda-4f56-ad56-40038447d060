#!/usr/bin/env python3
"""
测试Gemini API连接问题
"""

import requests
import socket
import ssl
import sys
import os

def test_dns_resolution():
    """测试DNS解析"""
    print("=== DNS解析测试 ===")
    hosts = [
        "generativelanguage.googleapis.com",
        "googleapis.com", 
        "google.com",
        "*******"
    ]
    
    for host in hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ {host} -> {ip}")
        except Exception as e:
            print(f"❌ {host} -> 解析失败: {e}")

def test_tcp_connection():
    """测试TCP连接"""
    print("\n=== TCP连接测试 ===")
    hosts = [
        ("generativelanguage.googleapis.com", 443),
        ("googleapis.com", 443),
        ("google.com", 443),
        ("*******", 53)
    ]
    
    for host, port in hosts:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {host}:{port} - 连接成功")
            else:
                print(f"❌ {host}:{port} - 连接失败 (错误码: {result})")
        except Exception as e:
            print(f"❌ {host}:{port} - 连接异常: {e}")

def test_ssl_connection():
    """测试SSL连接"""
    print("\n=== SSL连接测试 ===")
    hosts = [
        "generativelanguage.googleapis.com",
        "googleapis.com"
    ]
    
    for host in hosts:
        try:
            context = ssl.create_default_context()
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            with context.wrap_socket(sock, server_hostname=host) as ssock:
                ssock.connect((host, 443))
                cert = ssock.getpeercert()
                print(f"✅ {host} - SSL连接成功")
                print(f"   证书主题: {cert.get('subject', 'N/A')}")
                
        except Exception as e:
            print(f"❌ {host} - SSL连接失败: {e}")

def test_http_request():
    """测试HTTP请求"""
    print("\n=== HTTP请求测试 ===")
    
    # 测试基本的Google服务
    urls = [
        "https://www.google.com",
        "https://googleapis.com",
        "https://generativelanguage.googleapis.com"
    ]
    
    for url in urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url} - HTTP状态: {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"❌ {url} - 请求超时")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ {url} - 连接错误: {e}")
        except Exception as e:
            print(f"❌ {url} - 其他错误: {e}")

def test_gemini_api_endpoint():
    """测试Gemini API端点"""
    print("\n=== Gemini API端点测试 ===")
    
    api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
    
    # 测试不同的API端点
    endpoints = [
        f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}",
        f"https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key={api_key}",
        f"https://ai.google.dev/api/rest/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    ]
    
    test_payload = {
        "contents": [{
            "parts": [{"text": "Hello, this is a test message."}]
        }]
    }
    
    for endpoint in endpoints:
        try:
            print(f"测试端点: {endpoint.split('?')[0]}")
            response = requests.post(
                endpoint, 
                json=test_payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            print(f"✅ 状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"✅ 响应成功!")
                break
            else:
                print(f"❌ 响应内容: {response.text[:200]}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")

def check_proxy_settings():
    """检查代理设置"""
    print("\n=== 代理设置检查 ===")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var} = {value}")
        else:
            print(f"❌ {var} = 未设置")

def main():
    print("Gemini API连接诊断工具")
    print("=" * 50)
    
    check_proxy_settings()
    test_dns_resolution()
    test_tcp_connection()
    test_ssl_connection()
    test_http_request()
    test_gemini_api_endpoint()
    
    print("\n=== 诊断完成 ===")

if __name__ == "__main__":
    main()
