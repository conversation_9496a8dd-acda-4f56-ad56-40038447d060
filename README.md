# Jobs Application LinkedIn AIHawk

🤖 **智能LinkedIn求职助手** - 基于AI的自动化求职申请系统

## 🌟 功能特色

### 📋 **简历智能优化**
- **PDF简历上传**：支持各种格式的简历解析
- **AI智能优化**：基于Gemini 2.0 Flash的智能简历优化
- **职位匹配**：根据目标职位自动调整简历内容
- **HTML预览**：实时预览优化后的简历效果
- **PDF生成**：一键生成专业格式的PDF简历

### 🔍 **LinkedIn自动化**
- **职位搜索**：自动搜索LinkedIn职位
- **智能筛选**：根据条件筛选合适的职位
- **自动申请**：支持Easy Apply自动申请
- **多种模式**：支持Selenium和Playwright两种自动化方式

### 🎯 **智能匹配**
- **职位解析**：智能解析职位要求和描述
- **技能匹配**：自动匹配用户技能与职位需求
- **个性化优化**：为每个职位生成定制化简历

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- Chrome浏览器

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/YourUsername/Jobs-Application_Linkedin_AIHawk.git
cd Jobs-Application_Linkedin_AIHawk
```

2. **安装Python依赖**
```bash
python -m venv virtual
virtual\Scripts\activate  # Windows
# source virtual/bin/activate  # Linux/Mac
pip install -r requirements.txt
```

3. **安装前端依赖**
```bash
cd webui/frontend
npm install
cd ../..
```

4. **配置API密钥**
```bash
# 复制配置文件
cp config.py.example config.py
cp secrets.yaml.example secrets.yaml

# 编辑配置文件，添加您的API密钥
```

### 启动服务

1. **启动后端服务**
```bash
cd webui/backend
python main.py
```

2. **启动前端服务**
```bash
cd webui/frontend
npm start
```

3. **访问应用**
打开浏览器访问: `http://localhost:3000`

## 📖 使用指南

### 简历优化流程

1. **上传简历**：在Web界面上传您的PDF简历
2. **输入职位信息**：粘贴LinkedIn职位URL或手动输入职位描述
3. **AI优化**：系统自动分析并优化您的简历
4. **预览和调整**：在HTML预览中查看优化效果
5. **下载PDF**：生成并下载优化后的简历

### LinkedIn自动化

1. **配置账户**：在配置文件中设置LinkedIn账户信息
2. **设置搜索条件**：配置职位搜索关键词和筛选条件
3. **启动自动化**：运行自动化脚本开始申请职位
4. **监控进度**：查看申请日志和结果统计

## ⚙️ 配置说明

### API配置
- **Gemini API**：用于AI简历优化
- **LinkedIn API**：用于职位信息获取

### 自动化配置
- **搜索关键词**：设置职位搜索关键词
- **地理位置**：设置目标工作地点
- **申请限制**：设置每日申请数量限制

## 🛠️ 技术栈

### 后端
- **Python 3.8+**
- **FastAPI**：Web API框架
- **Selenium/Playwright**：浏览器自动化
- **Google Gemini**：AI语言模型

### 前端
- **React 18**
- **Material-UI**：UI组件库
- **Vite**：构建工具

### 其他
- **PyPDF2**：PDF处理
- **BeautifulSoup**：HTML解析
- **Requests**：HTTP客户端

## 📁 项目结构

```
Jobs-Application_Linkedin_AIHawk/
├── src/                          # 核心源代码
│   ├── libs/                     # 核心库
│   │   ├── resume_and_cover_builder/  # 简历生成器
│   │   └── llm_manager.py        # LLM管理器
│   └── linkedin_automation_*.py  # LinkedIn自动化
├── webui/                        # Web界面
│   ├── backend/                  # 后端API
│   └── frontend/                 # React前端
├── config.py                     # 主配置文件
├── requirements.txt              # Python依赖
└── README.md                     # 项目说明
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本工具仅供学习和研究使用。使用时请遵守LinkedIn的服务条款和相关法律法规。

## 🙏 致谢

- [AIHawk](https://github.com/feder-cr/AIHawk) - 原始项目灵感
- [Google Gemini](https://ai.google.dev/) - AI语言模型支持
- [Material-UI](https://mui.com/) - UI组件库

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！
