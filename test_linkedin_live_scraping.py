#!/usr/bin/env python3
"""
LinkedIn实时职位抓取测试脚本
使用Playwright直接抓取LinkedIn职位页面
"""

import sys
import os
import time
import logging
from pathlib import Path
from playwright.sync_api import sync_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_linkedin_job_scraping():
    """测试LinkedIn职位抓取"""
    logger.info("开始测试LinkedIn实时职位抓取...")
    
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-blink-features=AutomationControlled']
        )
        context = browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            viewport={'width': 1200, 'height': 800}
        )
        page = context.new_page()
        page.set_default_timeout(60000)  # 设置60秒超时

        try:
            # 先访问LinkedIn主页
            logger.info("先访问LinkedIn主页...")
            page.goto("https://www.linkedin.com", timeout=60000)
            time.sleep(2)

            # 访问LinkedIn职位搜索页面
            search_url = "https://www.linkedin.com/jobs/search/?keywords=python%20developer&location=United%20States&f_AL=true"
            logger.info(f"访问LinkedIn职位搜索页面: {search_url}")

            page.goto(search_url, timeout=60000, wait_until="domcontentloaded")
            time.sleep(5)  # 等待页面完全加载
            
            # 保存页面快照
            os.makedirs('log', exist_ok=True)
            page.screenshot(path="log/linkedin_jobs_page.png")
            
            # 保存HTML内容
            html_content = page.content()
            with open("log/linkedin_jobs_live.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            
            logger.info("页面快照和HTML已保存")
            
            # 尝试多种职位卡片选择器
            job_selectors = [
                "li[data-job-id]",  # 新版LinkedIn
                ".job-card-container",  # 旧版LinkedIn
                ".base-search-card",  # 通用搜索卡片
                ".job-card-list__item",  # 列表项
                "li:has(.artdeco-entity-lockup)",  # 包含实体锁定的li
                "li:has(a[href*='/jobs/view/'])"  # 包含职位链接的li
            ]
            
            jobs_found = []
            
            for selector in job_selectors:
                try:
                    job_cards = page.query_selector_all(selector)
                    logger.info(f"选择器 '{selector}' 找到 {len(job_cards)} 个元素")
                    
                    if job_cards:
                        for i, card in enumerate(job_cards[:5]):  # 只处理前5个
                            try:
                                # 提取职位信息
                                job_info = extract_job_info(card)
                                if job_info and job_info.get('title') != '未知职位':
                                    jobs_found.append(job_info)
                                    logger.info(f"提取到职位: {job_info.get('title')} - {job_info.get('company')}")
                            except Exception as e:
                                logger.warning(f"提取第{i+1}个职位信息失败: {str(e)}")
                        
                        if jobs_found:
                            break  # 找到有效职位就停止尝试其他选择器
                            
                except Exception as e:
                    logger.warning(f"选择器 '{selector}' 执行失败: {str(e)}")
            
            # 显示结果
            print(f"\n=== 抓取结果 ===")
            print(f"找到职位数量: {len(jobs_found)}")
            
            for i, job in enumerate(jobs_found):
                print(f"\n--- 职位 {i+1} ---")
                print(f"标题: {job.get('title', 'N/A')}")
                print(f"公司: {job.get('company', 'N/A')}")
                print(f"地点: {job.get('location', 'N/A')}")
                print(f"Easy Apply: {job.get('is_easy_apply', False)}")
                print(f"URL: {job.get('url', 'N/A')}")
            
            # 如果没有找到职位，尝试分析页面内容
            if not jobs_found:
                logger.warning("未找到职位信息，分析页面内容...")
                analyze_page_content(page)
            
            return len(jobs_found) > 0
            
        except Exception as e:
            logger.error(f"抓取过程中发生错误: {str(e)}")
            return False
        finally:
            browser.close()

def extract_job_info(job_card):
    """从职位卡片提取信息（使用LinkedIn实际结构）"""
    try:
        # 职位标题 - 使用LinkedIn实际选择器
        title_selectors = [
            ".base-search-card__title",  # LinkedIn实际使用的选择器
            "h3",
            ".job-card-list__title"
        ]

        title = "未知职位"
        job_url = None

        for selector in title_selectors:
            title_elem = job_card.query_selector(selector)
            if title_elem:
                title_text = title_elem.text_content().strip()
                # 过滤掉星号遮挡的内容
                if title_text and not title_text.startswith('***'):
                    title = title_text
                    break

        # 职位链接 - 使用LinkedIn实际选择器
        link_selectors = [
            ".base-card__full-link",  # LinkedIn实际使用的选择器
            "a[href*='/jobs/view/']"
        ]

        for selector in link_selectors:
            link_elem = job_card.query_selector(selector)
            if link_elem:
                job_url = link_elem.get_attribute('href')
                if job_url and not job_url.startswith('http'):
                    job_url = f"https://www.linkedin.com{job_url}"
                break

        # 公司名称 - 使用LinkedIn实际选择器
        company_selectors = [
            ".base-search-card__subtitle a",  # LinkedIn实际使用的选择器
            ".base-search-card__subtitle",
            ".job-card-container__company-name"
        ]

        company = "未知公司"
        for selector in company_selectors:
            company_elem = job_card.query_selector(selector)
            if company_elem:
                company_text = company_elem.text_content().strip()
                # 过滤掉星号遮挡的内容
                if company_text and not company_text.startswith('***'):
                    company = company_text
                    break

        # 地点 - 使用LinkedIn实际选择器
        location_selectors = [
            ".job-search-card__location",  # LinkedIn实际使用的选择器
            ".base-search-card__metadata span",
            ".job-card-list__location"
        ]

        location = "未知地点"
        for selector in location_selectors:
            location_elem = job_card.query_selector(selector)
            if location_elem:
                location_text = location_elem.text_content().strip()
                if location_text and not any(word in location_text.lower() for word in ['ago', 'day', 'week', 'month', 'hour', '前', '小时', '天']):
                    location = location_text
                    break

        # Easy Apply检测 - 检查是否有Easy Apply相关文本
        is_easy_apply = False
        card_html = job_card.inner_html()
        if ("Easy Apply" in card_html or "轻松申请" in card_html or
            "抢先申请" in card_html or "正在招聘" in card_html):
            is_easy_apply = True

        # 提取Job ID
        job_id = None
        data_entity_urn = job_card.get_attribute('data-entity-urn')
        if data_entity_urn:
            # 从data-entity-urn中提取job ID
            import re
            match = re.search(r'jobPosting:(\d+)', data_entity_urn)
            if match:
                job_id = match.group(1)

        return {
            'title': title,
            'company': company,
            'location': location,
            'url': job_url,
            'is_easy_apply': is_easy_apply,
            'job_id': job_id
        }

    except Exception as e:
        logger.error(f"提取职位信息失败: {str(e)}")
        return None

def analyze_page_content(page):
    """分析页面内容，帮助调试"""
    try:
        # 检查页面标题
        title = page.title()
        logger.info(f"页面标题: {title}")
        
        # 检查是否有登录提示
        login_elements = page.query_selector_all("text=Sign in")
        if login_elements:
            logger.warning("页面包含登录提示，可能需要先登录")
        
        # 检查是否有职位相关的文本
        job_text_elements = page.query_selector_all("text=/job|position|career/i")
        logger.info(f"找到 {len(job_text_elements)} 个包含职位相关文本的元素")
        
        # 检查所有链接
        all_links = page.query_selector_all("a")
        job_links = [link for link in all_links if "/jobs/view/" in (link.get_attribute('href') or '')]
        logger.info(f"找到 {len(job_links)} 个职位链接")
        
        # 检查页面是否被重定向
        current_url = page.url
        logger.info(f"当前URL: {current_url}")
        
    except Exception as e:
        logger.error(f"分析页面内容失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 LinkedIn实时职位抓取测试")
    print("=" * 50)
    
    success = test_linkedin_job_scraping()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果:")
    if success:
        print("✅ 成功抓取到职位信息！")
        print("💡 建议：")
        print("  - 检查抓取的职位信息质量")
        print("  - 优化选择器以提高准确性")
        print("  - 集成到主程序中")
    else:
        print("❌ 未能抓取到职位信息")
        print("💡 建议：")
        print("  - 检查是否需要登录LinkedIn")
        print("  - 更新CSS选择器")
        print("  - 检查网络连接和代理设置")

if __name__ == "__main__":
    main()
