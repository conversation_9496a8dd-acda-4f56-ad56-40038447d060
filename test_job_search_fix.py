#!/usr/bin/env python3
"""
测试修复后的LinkedIn职位搜索功能
专门测试风控检测和浏览器管理的修复
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.linkedin_automation_playwright import LinkedInAutomationPlaywright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_job_search_without_restart():
    """测试职位搜索不会意外重启浏览器"""
    logger.info("测试职位搜索功能（修复后）...")
    
    try:
        # 创建LinkedIn自动化实例
        test_config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20121108@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        with LinkedInAutomationPlaywright(test_config) as linkedin:
            # 初始化浏览器
            logger.info("步骤1: 初始化浏览器")
            page = linkedin.setup_driver()
            initial_page_id = id(page)
            logger.info(f"初始页面ID: {initial_page_id}")
            
            # 访问LinkedIn主页
            logger.info("步骤2: 访问LinkedIn主页")
            page.goto("https://www.linkedin.com")
            time.sleep(2)
            
            # 尝试登录（可选）
            logger.info("步骤3: 尝试登录")
            login_result = linkedin.login()
            logger.info(f"登录结果: {login_result.get('status', '未知')}")
            
            # 检查页面ID是否变化
            current_page_id = id(linkedin.page)
            if current_page_id != initial_page_id:
                logger.warning(f"⚠️ 页面ID已变化: {initial_page_id} -> {current_page_id}")
            else:
                logger.info("✅ 页面ID未变化，浏览器实例保持稳定")
            
            # 搜索职位
            logger.info("步骤4: 搜索职位")
            search_start_time = time.time()
            
            jobs = linkedin.search_jobs(
                keywords='python developer',
                location='United States',
                easy_apply_only=False
            )
            
            search_end_time = time.time()
            search_duration = search_end_time - search_start_time
            
            # 检查搜索后页面ID是否变化
            final_page_id = id(linkedin.page)
            if final_page_id != current_page_id:
                logger.warning(f"⚠️ 搜索后页面ID变化: {current_page_id} -> {final_page_id}")
                logger.warning("这表明浏览器在搜索过程中被重启了")
            else:
                logger.info("✅ 搜索后页面ID未变化，浏览器实例保持稳定")
            
            logger.info(f"搜索完成，耗时: {search_duration:.2f}秒")
            logger.info(f"找到职位数量: {len(jobs)}")
            
            # 显示前3个职位
            for i, job in enumerate(jobs[:3]):
                logger.info(f"职位 {i+1}: {job.get('title', 'N/A')} - {job.get('company', 'N/A')}")
            
            # 验证职位质量
            valid_jobs = sum(1 for job in jobs if (
                job.get('title') != '未知职位' and 
                job.get('company') != '未知公司' and 
                job.get('url') and '/jobs/view/' in job.get('url', '')
            ))
            
            logger.info(f"有效职位数: {valid_jobs}")
            logger.info(f"有效率: {valid_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
            
            # 判断测试结果
            if len(jobs) > 0 and valid_jobs > 0:
                logger.info("✅ 职位搜索功能正常")
                return True
            else:
                logger.warning("⚠️ 职位搜索结果质量不佳")
                return False
                
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return False

def test_wind_control_detection():
    """测试风控检测逻辑"""
    logger.info("测试风控检测逻辑...")
    
    try:
        test_config = {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': True,
                'timeout': 30000
            }
        }
        
        linkedin = LinkedInAutomationPlaywright(test_config)
        
        # 测试正常页面
        normal_content = """
        <html>
        <body>
            <div class="base-search-card">
                <h3>Python Developer</h3>
                <span class="job-search-card__location">New York</span>
            </div>
            <div class="jobs-search-results-list">
                More jobs here...
            </div>
        </body>
        </html>
        """
        
        is_blocked = linkedin.is_linkedin_blocked(normal_content)
        logger.info(f"正常页面检测结果: {'被风控' if is_blocked else '正常'}")
        assert not is_blocked, "正常页面被误判为风控"
        
        # 测试风控页面
        blocked_content = """
        <html>
        <body>
            <div>Please complete this security check</div>
            <div>We detected unusual activity</div>
            <div>Verify you're human</div>
        </body>
        </html>
        """
        
        is_blocked = linkedin.is_linkedin_blocked(blocked_content)
        logger.info(f"风控页面检测结果: {'被风控' if is_blocked else '正常'}")
        assert is_blocked, "风控页面未被检测到"
        
        # 测试混合页面（有风控关键词但也有正常内容）
        mixed_content = """
        <html>
        <body>
            <div class="base-search-card">
                <h3>Python Developer</h3>
            </div>
            <div>Some ad about security check</div>
            <div class="jobs-search-results-list">Jobs here</div>
        </body>
        </html>
        """
        
        is_blocked = linkedin.is_linkedin_blocked(mixed_content)
        logger.info(f"混合页面检测结果: {'被风控' if is_blocked else '正常'}")
        assert not is_blocked, "混合页面被误判为风控"
        
        logger.info("✅ 风控检测逻辑测试通过")
        return True
        
    except Exception as e:
        logger.error(f"风控检测测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 LinkedIn职位搜索修复验证")
    print("=" * 50)
    
    # 创建log目录
    os.makedirs('log', exist_ok=True)
    
    # 测试1: 风控检测逻辑
    print("\n🛡️ 测试1: 风控检测逻辑")
    detection_success = test_wind_control_detection()
    
    # 测试2: 职位搜索功能
    print("\n🔍 测试2: 职位搜索功能")
    search_success = test_job_search_without_restart()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 修复验证结果:")
    print(f"风控检测: {'✅ 成功' if detection_success else '❌ 失败'}")
    print(f"职位搜索: {'✅ 成功' if search_success else '❌ 失败'}")
    
    if detection_success and search_success:
        print("\n🎉 LinkedIn职位搜索功能修复成功！")
        print("💡 修复内容：")
        print("  - 优化了风控检测逻辑，减少误判")
        print("  - 修复了浏览器意外重启的问题")
        print("  - 改进了职位内容加载检测")
        print("  - 增强了搜索稳定性")
        print("\n🚀 现在可以稳定进行LinkedIn职位搜索了！")
    else:
        print("\n⚠️ 还有一些问题需要解决")
        print("💡 建议：")
        print("  - 检查网络连接和LinkedIn访问")
        print("  - 确认登录状态")
        print("  - 查看详细错误日志")

if __name__ == "__main__":
    main()
