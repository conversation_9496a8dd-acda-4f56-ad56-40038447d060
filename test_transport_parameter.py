#!/usr/bin/env python3
"""
测试transport参数是否生效
"""

import sys
import os
import time

def test_transport_parameter():
    """测试transport参数"""
    print("=== 测试transport参数 ===")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        print("创建使用REST传输的LLM实例...")
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=api_key,
            temperature=0.4,
            request_timeout=30.0,
            max_retries=2,
            transport="rest",
            verbose=True
        )
        
        print("LLM实例创建成功")
        print(f"LLM配置: {llm}")
        
        # 检查内部配置
        if hasattr(llm, '_client'):
            print(f"客户端类型: {type(llm._client)}")
            if hasattr(llm._client, '_transport'):
                print(f"传输类型: {llm._client._transport}")
        
        print("尝试调用LLM...")
        start_time = time.time()
        
        # 设置较短的超时时间来快速测试
        response = llm.invoke("Hello, respond with 'Transport test successful'")
        
        end_time = time.time()
        print(f"✅ 调用成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"响应: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_without_transport():
    """测试不指定transport参数（默认gRPC）"""
    print("\n=== 测试默认传输（gRPC） ===")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
        
        print("创建使用默认传输的LLM实例...")
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=api_key,
            temperature=0.4,
            request_timeout=10.0,  # 较短超时
            max_retries=1,
            verbose=True
        )
        
        print("LLM实例创建成功")
        
        print("尝试调用LLM（应该会超时）...")
        start_time = time.time()
        
        response = llm.invoke("Hello")
        
        end_time = time.time()
        print(f"✅ 意外成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"响应: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预期的失败: {str(e)}")
        return False

def main():
    print("Transport参数测试")
    print("=" * 50)
    
    # 测试REST传输
    rest_success = test_transport_parameter()
    
    # 测试默认传输
    grpc_success = test_without_transport()
    
    print("\n=== 总结 ===")
    print(f"REST传输: {'✅ 成功' if rest_success else '❌ 失败'}")
    print(f"默认传输: {'✅ 成功' if grpc_success else '❌ 失败（预期）'}")
    
    if rest_success and not grpc_success:
        print("\n🎉 transport='rest'参数生效！")
    elif rest_success and grpc_success:
        print("\n⚠️ 两种传输都成功，可能网络环境已改善")
    else:
        print("\n❌ transport参数可能未生效")

if __name__ == "__main__":
    main()
