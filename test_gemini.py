#!/usr/bin/env python3
"""
测试Gemini API连接
"""
import os
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gemini_api():
    """测试Gemini API连接"""
    api_key = "AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw"
    
    # 测试基本的文本生成
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "Hello, please respond with 'API connection successful'"
                    }
                ]
            }
        ]
    }
    
    try:
        print("正在测试Gemini API连接...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Gemini API连接成功!")
            print(f"响应: {result}")
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 - 可能需要VPN")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误 - 请检查网络连接")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False

def test_langchain_gemini():
    """测试LangChain Gemini集成"""
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.prompts import ChatPromptTemplate
        from langchain_core.output_parsers import StrOutputParser

        print("\n正在测试LangChain Gemini集成...")

        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key="AIzaSyAjt8qr1kIeVXbK075wXM1qcdeXnpH81Aw",
            temperature=0.4,
            request_timeout=30.0,
            max_retries=2
        )

        # 测试简单调用
        response = llm.invoke("Hello, please respond with 'LangChain integration successful'")
        print("✅ LangChain Gemini集成成功!")
        print(f"响应: {response.content}")

        # 测试链式调用（模拟职位解析）
        print("\n测试职位信息提取...")
        prompt = ChatPromptTemplate.from_template(
            "从以下职位描述中提取公司名称：\n\n{job_description}\n\n公司名称："
        )
        chain = prompt | llm | StrOutputParser()

        test_job = "MathWorks is hiring a Software Engineer in Boston. Join our team!"
        result = chain.invoke({"job_description": test_job})
        print(f"提取结果: {result}")

        return True

    except Exception as e:
        print(f"❌ LangChain集成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== Gemini API 连接测试 ===")
    
    # 测试直接API调用
    api_success = test_gemini_api()
    
    # 测试LangChain集成
    langchain_success = test_langchain_gemini()
    
    print("\n=== 测试结果 ===")
    print(f"直接API调用: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"LangChain集成: {'✅ 成功' if langchain_success else '❌ 失败'}")
    
    if api_success and langchain_success:
        print("\n🎉 所有测试通过！Gemini API工作正常")
        sys.exit(0)
    else:
        print("\n⚠️  部分测试失败，请检查网络连接或VPN设置")
        sys.exit(1)
