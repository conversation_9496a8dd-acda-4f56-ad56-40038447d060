#!/usr/bin/env python3
"""
LinkedIn Playwright问题修复测试
专门测试LinkedIn自动化中的Playwright问题
"""

import asyncio
import sys
import traceback
import logging
import time
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_linkedin_playwright_sync():
    """测试LinkedIn Playwright同步版本"""
    logger.info("=== 测试LinkedIn Playwright同步版本 ===")
    try:
        from src.linkedin_automation_playwright import LinkedInAutomationPlaywright
        
        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        logger.info("1. 创建LinkedIn自动化实例...")
        linkedin = LinkedInAutomationPlaywright(config)
        logger.info("LinkedIn自动化实例创建成功")
        
        logger.info("2. 设置浏览器...")
        page = linkedin.setup_driver(headless=False)
        logger.info("浏览器设置成功")
        
        logger.info("3. 访问LinkedIn主页...")
        try:
            # 先尝试访问Google测试网络连接
            page.goto("https://www.google.com", timeout=30000)
            logger.info("网络连接正常，访问LinkedIn...")
            page.goto("https://www.linkedin.com", timeout=30000)
            time.sleep(2)
            title = page.title()
            logger.info(f"页面标题: {title}")
        except Exception as e:
            logger.error(f"网络连接失败: {str(e)}")
            # 尝试不使用代理
            logger.info("尝试重新设置浏览器（不使用代理）...")
            linkedin.close()
            page = linkedin.setup_driver(headless=False)
            page.goto("https://www.linkedin.com", timeout=30000)
            time.sleep(2)
            title = page.title()
            logger.info(f"页面标题: {title}")
        
        logger.info("4. 尝试登录...")
        login_result = linkedin.login()
        logger.info(f"登录结果: {login_result}")
        
        if login_result.get('success'):
            logger.info("✅ 登录成功")
        elif login_result.get('requires_action'):
            logger.info("⚠️ 需要用户操作（如2FA验证）")
            logger.info("请在浏览器中完成验证，然后按Enter继续...")
            input()
        else:
            logger.warning(f"❌ 登录失败: {login_result.get('status')}")
        
        logger.info("5. 清理资源...")
        linkedin.close()
        logger.info("✅ LinkedIn Playwright同步版本测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ LinkedIn Playwright同步版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

async def test_linkedin_playwright_async():
    """测试LinkedIn Playwright异步版本"""
    logger.info("=== 测试LinkedIn Playwright异步版本 ===")
    try:
        from src.linkedin_automation_playwright_async import LinkedInAutomationPlaywrightAsync
        
        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,
                'window_size': [1200, 800]
            }
        }
        
        logger.info("1. 创建LinkedIn异步自动化实例...")
        linkedin = LinkedInAutomationPlaywrightAsync(config)
        logger.info("LinkedIn异步自动化实例创建成功")
        
        logger.info("2. 设置异步浏览器...")
        page = await linkedin.setup_driver(headless=False)
        logger.info("异步浏览器设置成功")
        
        logger.info("3. 访问LinkedIn主页...")
        await page.goto("https://www.linkedin.com")
        await asyncio.sleep(2)
        title = await page.title()
        logger.info(f"页面标题: {title}")
        
        logger.info("4. 尝试异步登录...")
        login_result = await linkedin.login()
        logger.info(f"登录结果: {login_result}")
        
        if login_result.get('success'):
            logger.info("✅ 异步登录成功")
        elif login_result.get('requires_action'):
            logger.info("⚠️ 需要用户操作（如2FA验证）")
            logger.info("请在浏览器中完成验证...")
            # 在异步环境中等待用户操作
            await asyncio.sleep(30)  # 给用户30秒时间完成验证
        else:
            logger.warning(f"❌ 异步登录失败: {login_result.get('status')}")
        
        logger.info("5. 清理异步资源...")
        await linkedin.close()
        logger.info("✅ LinkedIn Playwright异步版本测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ LinkedIn Playwright异步版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_linkedin_selenium():
    """测试LinkedIn Selenium版本（作为对比）"""
    logger.info("=== 测试LinkedIn Selenium版本 ===")
    try:
        from src.linkedin_automation import LinkedInAutomation
        import yaml
        import tempfile
        import os

        # 配置
        config = {
            'linkedin': {
                'email': '<EMAIL>',
                'password': 'Ajh20181102@@',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'selenium': {
                'headless': False,
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'window_size': [1920, 1080]
            }
        }

        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config, f, default_flow_style=False)
            temp_config_path = f.name

        try:
            logger.info("1. 创建LinkedIn Selenium自动化实例...")
            linkedin = LinkedInAutomation(temp_config_path)
            logger.info("LinkedIn Selenium自动化实例创建成功")
        finally:
            # 清理临时文件
            os.unlink(temp_config_path)
        
        logger.info("2. 设置Selenium浏览器...")
        driver = linkedin.setup_driver(headless=False)
        logger.info("Selenium浏览器设置成功")
        
        logger.info("3. 访问LinkedIn主页...")
        driver.get("https://www.linkedin.com")
        time.sleep(2)
        title = driver.title
        logger.info(f"页面标题: {title}")
        
        logger.info("4. 尝试Selenium登录...")
        login_result = linkedin.login()
        logger.info(f"登录结果: {login_result}")
        
        if login_result.get('success'):
            logger.info("✅ Selenium登录成功")
        elif login_result.get('requires_action'):
            logger.info("⚠️ 需要用户操作（如2FA验证）")
            logger.info("请在浏览器中完成验证，然后按Enter继续...")
            input()
        else:
            logger.warning(f"❌ Selenium登录失败: {login_result.get('status')}")
        
        logger.info("5. 清理Selenium资源...")
        linkedin.close()
        logger.info("✅ LinkedIn Selenium版本测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ LinkedIn Selenium版本测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def run_async_linkedin_test():
    """运行异步LinkedIn测试"""
    try:
        # 检查是否已有事件循环
        try:
            loop = asyncio.get_running_loop()
            logger.info("检测到运行中的事件循环，跳过异步测试")
            logger.warning("在同步环境中无法正确测试异步代码")
            return False
        except RuntimeError:
            # 没有运行中的循环，创建新的
            logger.info("没有运行中的事件循环，创建新循环")
            return asyncio.run(test_linkedin_playwright_async())
    except Exception as e:
        logger.error(f"运行异步LinkedIn测试失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始LinkedIn Playwright问题修复测试...")
    
    # 测试顺序：先测试工作的Selenium，再测试Playwright
    
    # 1. 测试Selenium版本（应该工作）
    logger.info("\n" + "="*50)
    selenium_success = test_linkedin_selenium()
    
    # 2. 测试Playwright同步版本
    logger.info("\n" + "="*50)
    sync_success = test_linkedin_playwright_sync()
    
    # 3. 测试Playwright异步版本
    logger.info("\n" + "="*50)
    async_success = run_async_linkedin_test()
    
    # 总结
    logger.info("\n" + "="*50)
    logger.info("=== 测试总结 ===")
    logger.info(f"Selenium版本: {'✅ 成功' if selenium_success else '❌ 失败'}")
    logger.info(f"Playwright同步版本: {'✅ 成功' if sync_success else '❌ 失败'}")
    logger.info(f"Playwright异步版本: {'✅ 成功' if async_success else '❌ 失败'}")
    
    if selenium_success and not sync_success and not async_success:
        logger.error("只有Selenium工作，Playwright版本都有问题")
        logger.info("建议检查Playwright LinkedIn自动化代码的配置和初始化")
    elif selenium_success and sync_success and not async_success:
        logger.warning("Selenium和Playwright同步版本工作，异步版本有问题")
        logger.info("建议检查异步代码的事件循环处理")
    elif selenium_success and not sync_success and async_success:
        logger.warning("Selenium和Playwright异步版本工作，同步版本有问题")
        logger.info("建议检查同步代码的配置")
    else:
        logger.info("测试完成，请查看具体错误信息")

if __name__ == "__main__":
    main()
