@echo off
title AIHawk 启动脚本

:: 启动后端
echo 正在启动后端服务...
start "AIHawk Backend" cmd /k "cd webui\backend && ..\..\virtual\Scripts\activate.bat && uvicorn main:app --host 0.0.0.0 --port 8002 --reload"

:: 等待几秒确保后端启动
timeout /t 5

:: 启动前端
echo 正在启动前端服务...
start "AIHawk Frontend" cmd /k "cd webui\frontend && npm start"

:: 等待几秒后打开浏览器
timeout /t 10
start http://localhost:3000

echo.
echo 服务已启动，请手动关闭窗口退出。
echo 后端地址: http://localhost:8002
echo 前端地址: http://localhost:3000
echo.
pause
