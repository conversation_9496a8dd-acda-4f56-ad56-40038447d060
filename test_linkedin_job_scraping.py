#!/usr/bin/env python3
"""
LinkedIn职位抓取测试脚本
测试修复后的LinkedIn职位信息抓取功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.linkedin_automation_playwright import LinkedInAutomationPlaywright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('log/linkedin_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_job_scraping():
    """测试职位抓取功能"""
    
    # 创建log目录
    os.makedirs('log', exist_ok=True)
    
    # 测试配置
    test_config = {
        'linkedin': {
            'email': '<EMAIL>',
            'password': 'Ajh20121108@@',
            'search_keywords': ['python developer'],
            'location': 'United States',
            'max_applications_per_day': 5,
            'delay_between_applications': [2, 5]
        },
        'playwright': {
            'headless': False,
            'timeout': 30000,
            'window_size': [1200, 800]
        }
    }
    
    logger.info("开始测试LinkedIn职位抓取功能...")
    
    try:
        # 创建LinkedIn自动化实例
        with LinkedInAutomationPlaywright(test_config) as linkedin:
            logger.info("正在登录LinkedIn...")
            
            # 登录
            login_result = linkedin.login()
            if not login_result:
                logger.error("登录失败，无法继续测试")
                return False
            
            logger.info("登录成功，开始搜索职位...")
            
            # 搜索职位
            jobs = linkedin.search_jobs(
                keywords='python developer',
                location='United States',
                easy_apply_only=True
            )
            
            logger.info(f"搜索完成，找到 {len(jobs)} 个职位")
            
            # 显示前5个职位的详细信息
            for i, job in enumerate(jobs[:5]):
                print(f"\n=== 职位 {i+1} ===")
                print(f"标题: {job.get('title', 'N/A')}")
                print(f"公司: {job.get('company', 'N/A')}")
                print(f"地点: {job.get('location', 'N/A')}")
                print(f"Easy Apply: {job.get('is_easy_apply', False)}")
                print(f"URL: {job.get('url', 'N/A')}")
                print(f"Job ID: {job.get('job_id', 'N/A')}")
            
            # 验证抓取质量
            valid_jobs = 0
            easy_apply_jobs = 0
            
            for job in jobs:
                if (job.get('title') != '未知职位' and 
                    job.get('company') != '未知公司' and 
                    job.get('location') != '未知地点'):
                    valid_jobs += 1
                
                if job.get('is_easy_apply'):
                    easy_apply_jobs += 1
            
            print(f"\n=== 抓取质量统计 ===")
            print(f"总职位数: {len(jobs)}")
            print(f"有效职位数: {valid_jobs}")
            print(f"Easy Apply职位数: {easy_apply_jobs}")
            print(f"有效率: {valid_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
            print(f"Easy Apply比例: {easy_apply_jobs/len(jobs)*100:.1f}%" if jobs else "0%")
            
            # 判断测试是否成功
            if len(jobs) > 0 and valid_jobs > 0:
                logger.info("✅ 职位抓取测试成功！")
                return True
            else:
                logger.error("❌ 职位抓取测试失败：未能抓取到有效职位信息")
                return False
                
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_html_parsing():
    """测试HTML解析功能"""
    logger.info("测试HTML解析功能...")
    
    html_file = "log/linkedin_jobs_full_page.html"
    if not os.path.exists(html_file):
        logger.warning(f"HTML文件 {html_file} 不存在，跳过HTML解析测试")
        return True
    
    try:
        # 创建LinkedIn自动化实例（仅用于解析）
        test_config = {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer'],
                'location': 'United States'
            },
            'playwright': {
                'headless': True,
                'timeout': 30000
            }
        }

        linkedin = LinkedInAutomationPlaywright(test_config)
        
        # 解析HTML文件
        jobs = linkedin.parse_jobs_from_html(html_file)
        
        logger.info(f"HTML解析完成，找到 {len(jobs)} 个职位")
        
        # 显示前3个职位
        for i, job in enumerate(jobs[:3]):
            print(f"\n=== HTML解析职位 {i+1} ===")
            print(f"标题: {job.get('title', 'N/A')}")
            print(f"公司: {job.get('company', 'N/A')}")
            print(f"地点: {job.get('location', 'N/A')}")
            print(f"Easy Apply: {job.get('is_easy_apply', False)}")
        
        if len(jobs) > 0:
            logger.info("✅ HTML解析测试成功！")
            return True
        else:
            logger.warning("⚠️ HTML解析未找到职位，可能需要更新选择器")
            return False
            
    except Exception as e:
        logger.error(f"HTML解析测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 LinkedIn职位抓取功能测试")
    print("=" * 50)
    
    # 测试1: 实时职位抓取
    print("\n📋 测试1: 实时职位抓取")
    scraping_success = test_job_scraping()
    
    # 测试2: HTML解析
    print("\n📄 测试2: HTML解析功能")
    parsing_success = test_html_parsing()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"实时抓取: {'✅ 成功' if scraping_success else '❌ 失败'}")
    print(f"HTML解析: {'✅ 成功' if parsing_success else '❌ 失败'}")
    
    if scraping_success:
        print("\n🎉 LinkedIn职位抓取功能已修复！")
        print("💡 建议：")
        print("  - 可以开始使用批量申请功能")
        print("  - 定期检查选择器是否需要更新")
        print("  - 监控抓取质量和成功率")
    else:
        print("\n⚠️ 还需要进一步调试和修复")
        print("💡 建议：")
        print("  - 检查LinkedIn页面结构是否有变化")
        print("  - 更新CSS选择器")
        print("  - 检查登录状态和网络连接")

if __name__ == "__main__":
    main()
